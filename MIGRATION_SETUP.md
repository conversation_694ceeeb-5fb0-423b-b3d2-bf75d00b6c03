# Database Migration Setup Guide

## Problem

When running migrations for the first time on an empty database, you encounter this error:

```
[11:14:06 ERR] Failed executing DbCommand (58ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";

__EFMigrationsHistory does not exist, so the SELECT fails.
```

## Root Cause

This error occurs because:
1. No Entity Framework migration files have been created yet
2. Entity Framework tries to query the `__EFMigrationsHistory` table to check what migrations have been applied
3. On an empty database, this table doesn't exist, causing the query to fail

## Solution

### Option 1: Use the Built-in CLI Command (Recommended)

1. **Install Entity Framework tools** (if not already installed):
   ```bash
   dotnet tool install --global dotnet-ef
   ```

2. **Create the initial migration** using the new CLI command:
   ```bash
   cd iChat.Tool
   dotnet run -- create-migration InitialCreate
   ```

3. **Apply the migration**:
   ```bash
   dotnet run -- migrate
   ```

### Option 2: Use Entity Framework CLI Directly

1. **Install Entity Framework tools** (if not already installed):
   ```bash
   dotnet tool install --global dotnet-ef
   ```

2. **Create the initial migration**:
   ```bash
   cd iChat.Tool
   dotnet ef migrations add InitialCreate --startup-project . --project ../iChat.Shared
   ```

3. **Apply the migration**:
   ```bash
   dotnet run -- migrate
   ```

## What the Fix Does

The updated `MigrationService` now:

1. **Handles missing migration history gracefully** - Instead of failing when `__EFMigrationsHistory` doesn't exist, it catches the exception and proceeds with migration
2. **Ensures database creation** - Uses `EnsureCreatedAsync()` if the database doesn't exist
3. **Applies all migrations** - When migration history is missing, it applies all available migrations using `MigrateAsync()`

## Verification

After creating and applying the migration:

1. **Check database connection**:
   ```bash
   dotnet run -- migrate --check
   ```

2. **Verify tables were created** by connecting to your database and checking that the following tables exist:
   - `__EFMigrationsHistory`
   - `Users`
   - `ChatSessions`
   - `Messages`
   - `Documents`
   - `DocumentStores`
   - `DocumentSections`
   - `EscalatedQueries`

## Available Commands

The iChat.Tool now includes these migration-related commands:

- `dotnet run -- create-migration <name>` - Create a new migration
- `dotnet run -- migrate` - Apply pending migrations
- `dotnet run -- migrate --check` - Test database connection
- `dotnet run -- info` - Show all available commands

## Future Migrations

When you make changes to your Entity Framework models:

1. **Create a new migration**:
   ```bash
   dotnet run -- create-migration DescriptiveNameForYourChanges
   ```

2. **Apply the migration**:
   ```bash
   dotnet run -- migrate
   ```

This process ensures your database schema stays in sync with your code changes.
