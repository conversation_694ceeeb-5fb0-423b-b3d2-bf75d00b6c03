﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using iChat.Shared.Data;

#nullable disable

namespace iChat.Shared.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250926163122_AddDocumentStoreIsDefaultAndQueryStatusUpdates")]
    partial class AddDocumentStoreIsDefaultAndQueryStatusUpdates
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("iChat.Shared.Models.ChatSession", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("ChatSessions");
                });

            modelBuilder.Entity("iChat.Shared.Models.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DocumentStoreId")
                        .HasColumnType("uuid");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UploadedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DocumentStoreId");

                    b.HasIndex("UploadedBy");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("iChat.Shared.Models.DocumentSection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.PrimitiveCollection<float[]>("Embedding")
                        .IsRequired()
                        .HasColumnType("real[]");

                    b.Property<int>("SectionIndex")
                        .HasColumnType("integer");

                    b.Property<int>("TokenCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.ToTable("DocumentSections");
                });

            modelBuilder.Entity("iChat.Shared.Models.DocumentStore", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.ToTable("DocumentStores");
                });

            modelBuilder.Entity("iChat.Shared.Models.EscalatedQuery", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AssignedTo")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uuid");

                    b.Property<string>("Resolution")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("MessageId")
                        .IsUnique();

                    b.ToTable("EscalatedQueries");
                });

            modelBuilder.Entity("iChat.Shared.Models.Message", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double?>("Confidence")
                        .HasColumnType("double precision");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Escalated")
                        .HasColumnType("boolean");

                    b.Property<int>("Role")
                        .HasColumnType("integer");

                    b.Property<Guid>("SessionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SessionId");

                    b.ToTable("Messages");
                });

            modelBuilder.Entity("iChat.Shared.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Role")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("iChat.Shared.Models.ChatSession", b =>
                {
                    b.HasOne("iChat.Shared.Models.User", "User")
                        .WithMany("ChatSessions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("iChat.Shared.Models.Document", b =>
                {
                    b.HasOne("iChat.Shared.Models.DocumentStore", "DocumentStore")
                        .WithMany("Documents")
                        .HasForeignKey("DocumentStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("iChat.Shared.Models.User", "UploadedByUser")
                        .WithMany("Documents")
                        .HasForeignKey("UploadedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DocumentStore");

                    b.Navigation("UploadedByUser");
                });

            modelBuilder.Entity("iChat.Shared.Models.DocumentSection", b =>
                {
                    b.HasOne("iChat.Shared.Models.Document", "Document")
                        .WithMany("DocumentSections")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");
                });

            modelBuilder.Entity("iChat.Shared.Models.DocumentStore", b =>
                {
                    b.HasOne("iChat.Shared.Models.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("iChat.Shared.Models.EscalatedQuery", b =>
                {
                    b.HasOne("iChat.Shared.Models.User", "AssignedToUser")
                        .WithMany()
                        .HasForeignKey("AssignedTo")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("iChat.Shared.Models.Message", "Message")
                        .WithOne("EscalatedQuery")
                        .HasForeignKey("iChat.Shared.Models.EscalatedQuery", "MessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedToUser");

                    b.Navigation("Message");
                });

            modelBuilder.Entity("iChat.Shared.Models.Message", b =>
                {
                    b.HasOne("iChat.Shared.Models.ChatSession", "Session")
                        .WithMany("Messages")
                        .HasForeignKey("SessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Session");
                });

            modelBuilder.Entity("iChat.Shared.Models.ChatSession", b =>
                {
                    b.Navigation("Messages");
                });

            modelBuilder.Entity("iChat.Shared.Models.Document", b =>
                {
                    b.Navigation("DocumentSections");
                });

            modelBuilder.Entity("iChat.Shared.Models.DocumentStore", b =>
                {
                    b.Navigation("Documents");
                });

            modelBuilder.Entity("iChat.Shared.Models.Message", b =>
                {
                    b.Navigation("EscalatedQuery");
                });

            modelBuilder.Entity("iChat.Shared.Models.User", b =>
                {
                    b.Navigation("ChatSessions");

                    b.Navigation("Documents");
                });
#pragma warning restore 612, 618
        }
    }
}
