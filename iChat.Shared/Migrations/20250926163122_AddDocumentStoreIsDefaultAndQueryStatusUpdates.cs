﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace iChat.Shared.Migrations
{
    /// <inheritdoc />
    public partial class AddDocumentStoreIsDefaultAndQueryStatusUpdates : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsDefault",
                table: "DocumentStores",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsDef<PERSON>",
                table: "DocumentStores");
        }
    }
}
