using Microsoft.EntityFrameworkCore;
using iChat.Shared.Models;

namespace iChat.Shared.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<ChatSession> ChatSessions { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<Document> Documents { get; set; }
    public DbSet<DocumentStore> DocumentStores { get; set; }
    public DbSet<DocumentSection> DocumentSections { get; set; }
    public DbSet<EscalatedQuery> EscalatedQueries { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // User relationships
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Email).IsRequired();
            entity.Property(e => e.Name).IsRequired();
        });

        // ChatSession relationships
        modelBuilder.Entity<ChatSession>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.User)
                .WithMany(u => u.ChatSessions)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Message relationships
        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Session)
                .WithMany(s => s.Messages)
                .HasForeignKey(e => e.SessionId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // DocumentStore relationships
        modelBuilder.Entity<DocumentStore>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.CreatedByUser)
                .WithMany()
                .HasForeignKey(e => e.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Document relationships
        modelBuilder.Entity<Document>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.UploadedByUser)
                .WithMany(u => u.Documents)
                .HasForeignKey(e => e.UploadedBy)
                .OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.DocumentStore)
                .WithMany(ds => ds.Documents)
                .HasForeignKey(e => e.DocumentStoreId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // DocumentSection relationships
        modelBuilder.Entity<DocumentSection>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Document)
                .WithMany(d => d.DocumentSections)
                .HasForeignKey(e => e.DocumentId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.Property(e => e.Embedding);
        });

        // EscalatedQuery relationships
        modelBuilder.Entity<EscalatedQuery>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Message)
                .WithOne(m => m.EscalatedQuery)
                .HasForeignKey<EscalatedQuery>(e => e.MessageId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.AssignedToUser)
                .WithMany()
                .HasForeignKey(e => e.AssignedTo)
                .OnDelete(DeleteBehavior.SetNull);
        });
    }
}
