using System.ComponentModel.DataAnnotations;

namespace iChat.Shared.Models;

public class Document
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public Guid UploadedBy { get; set; }
    public Guid DocumentStoreId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public virtual User UploadedByUser { get; set; } = null!;
    public virtual DocumentStore DocumentStore { get; set; } = null!;
    public virtual ICollection<DocumentSection> DocumentSections { get; set; } = new List<DocumentSection>();
}
