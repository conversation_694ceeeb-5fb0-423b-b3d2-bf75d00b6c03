# Use the official Microsoft .NET SDK image as base
FROM mcr.microsoft.com/dotnet/sdk:9.0

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV DOTNET_CLI_TELEMETRY_OPTOUT=1
ENV DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
ENV DOTNET_NOLOGO=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    unzip \
    sudo \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    jq \
    vim \
    nano \
    htop \
    tree \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20.x (LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Install pnpm
RUN npm install -g pnpm@latest

# Install Supabase CLI
RUN curl -fsSL https://github.com/supabase/cli/releases/latest/download/supabase_linux_amd64.tar.gz \
    | tar -xz -C /usr/local/bin \
    && chmod +x /usr/local/bin/supabase

# Install PostgreSQL client tools
RUN apt-get update && apt-get install -y postgresql-client && rm -rf /var/lib/apt/lists/*

# Create a non-root user
ARG USERNAME=vscode
ARG USER_UID=1000
ARG USER_GID=$USER_UID

RUN groupadd --gid $USER_GID $USERNAME \
    && useradd --uid $USER_UID --gid $USER_GID -m $USERNAME \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

# Install .NET tools globally
RUN dotnet tool install --global dotnet-ef \
    && dotnet tool install --global dotnet-aspnet-codegenerator \
    && dotnet tool install --global dotnet-dev-certs \
    && dotnet tool install --global dotnet-format \
    && dotnet tool install --global dotnet-outdated-tool

# Add .NET tools to PATH
ENV PATH="$PATH:/root/.dotnet/tools"

# Set up workspace directory
WORKDIR /workspace

# Switch to non-root user
USER $USERNAME

# Add .NET tools to user PATH
ENV PATH="$PATH:/home/<USER>/.dotnet/tools"

# Install user-level .NET tools
RUN dotnet tool install --global dotnet-ef \
    && dotnet tool install --global dotnet-aspnet-codegenerator \
    && dotnet tool install --global dotnet-dev-certs \
    && dotnet tool install --global dotnet-format \
    && dotnet tool install --global dotnet-outdated-tool

# Set the default shell to bash
SHELL ["/bin/bash", "-c"]

# Expose common ports
EXPOSE 3000 5000 5001 8080 54321

# Set default command
CMD ["/bin/bash"]
