{"name": "iChat Development Environment", "build": {"dockerfile": "Dockerfile", "context": ".."}, "features": {"ghcr.io/devcontainers/features/common-utils:2": {"installZsh": true, "configureZshAsDefaultShell": true, "installOhMyZsh": true, "upgradePackages": true, "username": "vscode", "userUid": "automatic", "userGid": "automatic"}, "ghcr.io/devcontainers/features/git:1": {"ppa": true, "version": "latest"}, "ghcr.io/devcontainers/features/github-cli:1": {"installDirectlyFromGitHubRelease": true, "version": "latest"}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"moby": true, "azureDnsAutoDetection": true, "installDockerBuildx": true, "version": "latest", "dockerDashComposeVersion": "v2"}}, "customizations": {"vscode": {"extensions": ["ms-dotnettools.csharp", "ms-dotnettools.csdevkit", "ms-dotnettools.vscode-dotnet-runtime", "supabase.supabase", "patcx.vscode-nuget-gallery", "sourcegraph.amp", "kilocode.kilo-code", "editorconfig.editorconfig", "aliasadidev.nugetpackagemanagergui"], "settings": {"terminal.integrated.defaultProfile.linux": "zsh", "dotnet.defaultSolution": "iChat.sln", "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableImportCompletion": true, "omnisharp.enableRoslynAnalyzers": true, "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/node_modules": true, "**/.vs": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}}}}, "forwardPorts": [3000, 5000, 5001, 8080, 54321], "portsAttributes": {"3000": {"label": "Frontend Dev Server", "onAutoForward": "notify"}, "5000": {"label": ".NET HTTP", "onAutoForward": "notify"}, "5001": {"label": ".NET HTTPS", "onAutoForward": "notify"}, "8080": {"label": "Web Server", "onAutoForward": "notify"}, "54321": {"label": "Supabase Studio", "onAutoForward": "notify"}}, "initializeCommand": "bash .devcontainer/init.sh", "postCreateCommand": "bash .devcontainer/post-create.sh", "remoteUser": "vscode", "remoteEnv": {"PODMAN_USERNS": "keep-id", "BUILDAH_ISOLATION": "chroot", "GITHUB_USERNAME": "${localEnv:GITHUB_USERNAME}", "GITHUB_TOKEN": "${localEnv:GITHUB_TOKEN}", "GIT_USER_NAME": "${localEnv:GIT_USER_NAME}", "GIT_USER_EMAIL": "${localEnv:GIT_USER_EMAIL}"}, "containerUser": "vscode", "runArgs": ["--privileged", "--env-file", "${localWorkspaceFolder}/.devcontainer/.env"], "workspaceFolder": "/workspace", "mounts": ["source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached", "source=${localWorkspaceFolder}/.devcontainer/bashrc,target=/home/<USER>/.bashrc,type=bind,consistency=cached", "source=${localWorkspaceFolder}/.devcontainer/zshrc,target=/home/<USER>/.zshrc,type=bind,consistency=cached"]}