#!/bin/bash

# Post-create script for iChat development environment
echo "🚀 Setting up iChat development environment..."

# Make sure we're in the workspace directory
cd /workspace

# Set up git safe directory
git config --global --add safe.directory /workspace

# Restore .NET packages
echo "📦 Restoring .NET packages..."
if [ -f "iChat.sln" ]; then
    dotnet restore iChat.sln
else
    echo "⚠️  iChat.sln not found, skipping .NET restore"
fi

# Install dotnet-gitversion tool globally
dotnet tool install --global GitVersion.Tool

# Install Node.js dependencies if package.json exists
if [ -f "package.json" ]; then
    echo "📦 Installing Node.js dependencies..."
    pnpm install
elif [ -f "pnpm-workspace.yaml" ]; then
    echo "📦 Installing workspace dependencies..."
    pnpm install
else
    echo "ℹ️  No package.json found, skipping Node.js dependencies"
fi

# Set up development certificates for HTTPS
echo "🔐 Setting up development certificates..."
dotnet dev-certs https --trust 2>/dev/null || echo "⚠️  Could not set up HTTPS certificates (this is normal in containers)"

# Create useful aliases
echo "⚙️  Setting up aliases..."
cat >> ~/.bashrc << 'EOF'

# iChat Development Aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'

# .NET aliases
alias dr='dotnet run'
alias db='dotnet build'
alias dt='dotnet test'
alias dw='dotnet watch'
alias dc='dotnet clean'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git pull'
alias gd='git diff'
alias gb='git branch'
alias gco='git checkout'

# Supabase aliases
alias sb='supabase'
alias sbs='supabase start'
alias sbst='supabase stop'
alias sbr='supabase reset'
alias sbl='supabase logs'

# Project specific aliases
alias build-all='dotnet build iChat.sln'
alias test-all='dotnet test iChat.sln'
alias clean-all='dotnet clean iChat.sln'
alias tool='dotnet run --project iChat.Tool --'

EOF

# Copy to zshrc as well
cp ~/.bashrc ~/.zshrc

# Set up git configuration if not already set
if [ -z "$(git config --global user.name)" ]; then
    echo "ℹ️  Git user not configured. You may want to run:"
    echo "   git config --global user.name 'Your Name'"
    echo "   git config --global user.email '<EMAIL>'"
fi

# Check if Supabase CLI is working
echo "🔍 Checking Supabase CLI..."
if command -v supabase &> /dev/null; then
    echo "✅ Supabase CLI is installed: $(supabase --version)"
else
    echo "❌ Supabase CLI installation failed"
fi

# Check .NET installation
echo "🔍 Checking .NET installation..."
dotnet --version
echo "✅ .NET SDK is ready"

# Check Node.js installation
echo "🔍 Checking Node.js installation..."
node --version
npm --version
pnpm --version
echo "✅ Node.js and pnpm are ready"

# Display helpful information
echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Quick Start Commands:"
echo "  • Build all projects:     build-all"
echo "  • Test all projects:      test-all"
echo "  • Run iChat.Tool:         tool [command]"
echo "  • Start Supabase:         supabase start"
echo "  • View Supabase Studio:   http://localhost:54321"
echo ""
echo "🔧 Available Tools:"
echo "  • .NET SDK $(dotnet --version)"
echo "  • Node.js $(node --version)"
echo "  • pnpm $(pnpm --version)"
echo "  • Supabase CLI $(supabase --version 2>/dev/null || echo 'installed')"
echo "  • PostgreSQL client tools"
echo ""
echo "📁 Project Structure:"
echo "  • iChat.Shared/     - Shared models and utilities"
echo "  • iChat.Tool/       - Command-line administration tool"
echo "  • docs/             - Project documentation"
echo ""
echo "Happy coding! 🚀"
