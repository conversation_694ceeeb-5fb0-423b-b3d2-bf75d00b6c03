# iChat Development Container

This devcontainer provides a complete development environment for the iChat project with all necessary tools pre-installed and configured.

## What's Included

### Development Tools
- **.NET 9 SDK** - Latest .NET SDK for building C# projects
- **Node.js 20.x LTS** - JavaScript runtime for frontend development
- **pnpm** - Fast, disk space efficient package manager
- **Supabase CLI** - Command-line interface for Supabase
- **PostgreSQL Client** - Database client tools
- **Git** - Version control system
- **GitHub CLI** - Command-line interface for GitHub
- **Docker** - Container platform (Docker-in-Docker)

### .NET Global Tools
- `dotnet-ef` - Entity Framework Core tools
- `dotnet-aspnet-codegenerator` - ASP.NET Core scaffolding
- `dotnet-dev-certs` - Development certificate management
- `dotnet-format` - Code formatting tool
- `dotnet-outdated-tool` - Package update checker

### VS Code Extensions
- **C# Dev Kit** - Complete C# development experience
- **Supabase** - Supabase integration
- **GitHub Copilot** - AI-powered code completion
- **Docker** - Container management
- **Prettier** - Code formatting
- **Tailwind CSS** - CSS framework support
- **PowerShell** - PowerShell scripting support

## Quick Start

### Prerequisites
- [Visual Studio Code](https://code.visualstudio.com/)
- [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)

### Getting Started

1. **Open in Container**
   ```bash
   # Clone the repository
   git clone <repository-url>
   cd ichat
   
   # Open in VS Code
   code .
   
   # When prompted, click "Reopen in Container"
   # Or use Command Palette: "Dev Containers: Reopen in Container"
   ```

2. **Wait for Setup**
   - The container will build automatically (first time takes 5-10 minutes)
   - Post-create script will restore packages and set up the environment
   - You'll see a welcome message when ready

3. **Start Developing**
   ```bash
   # Build all projects
   build-all
   
   # Run the CLI tool
   tool --help
   
   # Start Supabase (if needed)
   supabase start
   ```

## Available Commands

### Project Commands
```bash
# Build all projects
build-all
dotnet build iChat.sln

# Test all projects  
test-all
dotnet test iChat.sln

# Clean all projects
clean-all
dotnet clean iChat.sln

# Run the CLI tool
tool [command]
dotnet run --project iChat.Tool -- [command]
```

### .NET Commands
```bash
dr          # dotnet run
db          # dotnet build  
dt          # dotnet test
dw          # dotnet watch
dc          # dotnet clean
```

### Supabase Commands
```bash
sb          # supabase
sbs         # supabase start
sbst        # supabase stop
sbr         # supabase reset
sbl         # supabase logs
```

### Git Commands
```bash
gs          # git status
ga          # git add
gc          # git commit
gp          # git push
gl          # git pull
gd          # git diff
gb          # git branch
gco         # git checkout
```

## Port Forwarding

The following ports are automatically forwarded:

| Port  | Service              | URL                        |
|-------|---------------------|----------------------------|
| 3000  | Frontend Dev Server | http://localhost:3000      |
| 5000  | .NET HTTP           | http://localhost:5000      |
| 5001  | .NET HTTPS          | https://localhost:5001     |
| 8080  | Web Server          | http://localhost:8080      |
| 54321 | Supabase Studio     | http://localhost:54321     |

## Environment Variables

Set these in your `.env` file or container environment:

```bash
# Supabase Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key

# .NET Configuration  
DOTNET_CLI_TELEMETRY_OPTOUT=1
DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
DOTNET_NOLOGO=1
```

## Troubleshooting

### Container Won't Start
- Ensure Docker Desktop is running
- Check available disk space (need ~2GB for container)
- Try rebuilding: Command Palette → "Dev Containers: Rebuild Container"

### .NET Restore Issues
```bash
# Manual restore
dotnet restore iChat.sln

# Clear NuGet cache
dotnet nuget locals all --clear
```

### Supabase Issues
```bash
# Check Supabase status
supabase status

# Reset Supabase
supabase stop
supabase start
```

### Permission Issues
```bash
# Fix file permissions
sudo chown -R vscode:vscode /workspace
```

## Customization

### Adding Extensions
Edit `.devcontainer/devcontainer.json`:
```json
"extensions": [
  "existing.extension",
  "new.extension.id"
]
```

### Adding Tools
Edit `.devcontainer/Dockerfile`:
```dockerfile
# Add your custom tools
RUN apt-get update && apt-get install -y your-tool
```

### Custom Aliases
Edit `.devcontainer/bashrc` or `.devcontainer/zshrc` to add your own aliases.

## Support

For issues with the devcontainer setup, please check:
1. [VS Code Dev Containers documentation](https://code.visualstudio.com/docs/devcontainers/containers)
2. [Docker documentation](https://docs.docker.com/)
3. Project issues on GitHub
