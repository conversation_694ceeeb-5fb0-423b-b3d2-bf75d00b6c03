# ~/.zshrc: executed by zsh for interactive shells.

# Path to your oh-my-zsh installation.
export ZSH="$HOME/.oh-my-zsh"

# Set name of the theme to load
ZSH_THEME="robbyrussell"

# Which plugins would you like to load?
plugins=(git dotnet node npm docker docker-compose)

# Load Oh My Zsh
if [ -f "$ZSH/oh-my-zsh.sh" ]; then
    source $ZSH/oh-my-zsh.sh
fi

# User configuration

# Add .NET tools to PATH
export PATH="$PATH:$HOME/.dotnet/tools"

# Set .NET environment variables
export DOTNET_CLI_TELEMETRY_OPTOUT=1
export DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
export DOTNET_NOLOGO=1

# Preferred editor for local and remote sessions
if [[ -n $SSH_CONNECTION ]]; then
  export EDITOR='vim'
else
  export EDITOR='code'
fi

# Set personal aliases, overriding those provided by oh-my-zsh libs,
# plugins, and themes. Aliases can be placed here, though oh-my-zsh
# users are encouraged to define aliases within the ZSH_CUSTOM folder.

# General aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'

# .NET aliases
alias dr='dotnet run'
alias db='dotnet build'
alias dt='dotnet test'
alias dw='dotnet watch'
alias dc='dotnet clean'

# Git aliases (additional to oh-my-zsh git plugin)
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git pull'
alias gd='git diff'
alias gb='git branch'
alias gco='git checkout'

# Supabase aliases
alias sb='supabase'
alias sbs='supabase start'
alias sbst='supabase stop'
alias sbr='supabase reset'
alias sbl='supabase logs'

# Project specific aliases
alias build-all='dotnet build iChat.sln'
alias test-all='dotnet test iChat.sln'
alias clean-all='dotnet clean iChat.sln'
alias tool='dotnet run --project iChat.Tool --'

# Enable auto-completion for dotnet CLI
_dotnet_zsh_complete() 
{
  local completions=("$(dotnet complete "$words")")

  # If the completion list is empty, just continue with filename selection
  if [ -z "$completions" ]
  then
    _arguments '*::arguments: _normal'
    return
  fi

  # This is not a variable assignment, don't remove spaces!
  _values = "${(ps:\n:)completions}"
}

compdef _dotnet_zsh_complete dotnet

# Welcome message for iChat development
echo "🚀 Welcome to the iChat Development Environment!"
echo "Type 'tool --help' to see available CLI commands"
