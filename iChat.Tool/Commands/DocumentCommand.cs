using Spectre.Console;
using Spectre.Console.Cli;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using iChat.Shared.Data;
using iChat.Shared.Models;
using System.ComponentModel;

namespace iChat.Tool.Commands;

public class DocumentSettings : CommandSettings
{
    [CommandOption("--help")]
    [Description("Show help and usage information")]
    public bool ShowHelp { get; set; }
}

public class DocumentCommand : Command<DocumentSettings>
{
    private readonly IServiceProvider _serviceProvider;

    public DocumentCommand(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public override int Execute(CommandContext context, DocumentSettings settings)
    {
        if (settings.ShowHelp)
        {
            ShowHelp();
            return 0;
        }

        ShowHelp();
        return 0;
    }

    private void ShowHelp()
    {
        var rule = new Rule("[blue]iChat.Tool - Document Commands[/]");
        rule.Justification = Justify.Left;
        AnsiConsole.Write(rule);
        AnsiConsole.WriteLine();

        var description = new Panel(
            "Manage documents in the iChat system.\n" +
            "Use subcommands to list, create, update, or delete documents.")
            .Header("[yellow]DESCRIPTION[/]")
            .BorderColor(Color.Yellow);
        AnsiConsole.Write(description);
        AnsiConsole.WriteLine();

        AnsiConsole.MarkupLine("[yellow]USAGE:[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document[/] [green][[subcommand]][/] [blue][[options]][/]");
        AnsiConsole.WriteLine();

        var commandsTable = new Table();
        commandsTable.Title = new TableTitle("[yellow]SUBCOMMANDS[/]");
        commandsTable.AddColumn("Subcommand");
        commandsTable.AddColumn("Description");

        commandsTable.AddRow("[green]list[/]", "List all documents with their document store and section count");
        commandsTable.AddRow("[green]create[/]", "Create a new document from a file");
        commandsTable.AddRow("[green]store list[/]", "List all document stores");
        commandsTable.AddRow("[green]store create[/]", "Create a new document store");
        commandsTable.AddRow("[green]store set-default[/]", "Set the default document store");

        AnsiConsole.Write(commandsTable);
        AnsiConsole.WriteLine();

        var optionsTable = new Table();
        optionsTable.Title = new TableTitle("[yellow]OPTIONS[/]");
        optionsTable.AddColumn("Option");
        optionsTable.AddColumn("Description");

        optionsTable.AddRow("[blue]--help[/]", "Show this help information");

        AnsiConsole.Write(optionsTable);
        AnsiConsole.WriteLine();

        AnsiConsole.MarkupLine("[yellow]EXAMPLES:[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document list[/]              [dim]# List all documents[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document create \"My Doc\" file.txt[/] [dim]# Create document from file[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document store list[/]        [dim]# List all document stores[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document store create \"Store\" \"Description\"[/] [dim]# Create store[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document --help[/]            [dim]# Show this help information[/]");
        AnsiConsole.WriteLine();
    }
}