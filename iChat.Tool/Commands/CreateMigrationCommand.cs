using Spectre.Console;
using Spectre.Console.Cli;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.ComponentModel;
using System.Diagnostics;

namespace iChat.Tool.Commands;

public class CreateMigrationSettings : CommandSettings
{
    [CommandArgument(0, "<name>")]
    [Description("Name of the migration to create")]
    public string Name { get; set; } = string.Empty;

    [CommandOption("--force")]
    [Description("Force creation even if migrations already exist")]
    public bool Force { get; set; }
}

public class CreateMigrationCommand : AsyncCommand<CreateMigrationSettings>
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public CreateMigrationCommand(IServiceProvider serviceProvider, IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, CreateMigrationSettings settings)
    {
        using var scope = _serviceProvider.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<CreateMigrationCommand>>();

        try
        {
            if (string.IsNullOrWhiteSpace(settings.Name))
            {
                AnsiConsole.MarkupLine("[red]❌ Migration name is required[/]");
                return 1;
            }

            // Check if migrations already exist
            var migrationsPath = Path.Combine("..", "iChat.Shared", "Migrations");
            if (Directory.Exists(migrationsPath) && Directory.GetFiles(migrationsPath, "*.cs").Any() && !settings.Force)
            {
                AnsiConsole.MarkupLine("[yellow]⚠️  Migrations already exist. Use --force to create anyway.[/]");
                return 1;
            }

            AnsiConsole.MarkupLine($"[blue]Creating migration '{settings.Name}'...[/]");

            await AnsiConsole.Status()
                .StartAsync("Creating migration...", async ctx =>
                {
                    ctx.Spinner(Spinner.Known.Dots);
                    ctx.SpinnerStyle(Style.Parse("green"));

                    // Run dotnet ef migrations add command
                    var startInfo = new ProcessStartInfo
                    {
                        FileName = "dotnet",
                        Arguments = $"ef migrations add {settings.Name} --startup-project . --project ../iChat.Shared",
                        WorkingDirectory = Directory.GetCurrentDirectory(),
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    };

                    using var process = Process.Start(startInfo);
                    if (process == null)
                    {
                        throw new InvalidOperationException("Failed to start dotnet ef process");
                    }

                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    if (process.ExitCode != 0)
                    {
                        throw new InvalidOperationException($"Migration creation failed: {error}");
                    }

                    logger.LogInformation($"Migration output: {output}");
                });

            AnsiConsole.MarkupLine($"[green]✅ Migration '{settings.Name}' created successfully[/]");
            AnsiConsole.MarkupLine("[yellow]💡 You can now run 'iChat.Tool migrate' to apply the migration[/]");
            
            return 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Create migration command failed");
            AnsiConsole.MarkupLine($"[red]❌ Migration creation failed: {ex.Message}[/]");
            
            // Show helpful information
            AnsiConsole.MarkupLine("[yellow]💡 Make sure you have the Entity Framework tools installed:[/]");
            AnsiConsole.MarkupLine("[cyan]   dotnet tool install --global dotnet-ef[/]");
            
            return 1;
        }
    }
}
