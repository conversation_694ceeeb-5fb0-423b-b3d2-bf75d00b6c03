using Spectre.Console;
using Spectre.Console.Cli;
using iChat.Services.Interfaces;
using iChat.Shared.Enums;
using System.ComponentModel;

namespace iChat.Tool.Commands;

public class UserCommands : Command
{
    public override int Execute(CommandContext context)
    {
        AnsiConsole.WriteLine("User management commands. Use --help for available subcommands.");
        return 0;
    }
}

[Description("List all users")]
public class UserListCommand : AsyncCommand
{
    private readonly IUserService _userService;

    public UserListCommand(IUserService userService)
    {
        _userService = userService;
    }

    public override async Task<int> ExecuteAsync(CommandContext context)
    {
        try
        {
            var users = await _userService.GetAllUsersAsync();

            var table = new Table();
            table.AddColumn("ID");
            table.AddColumn("Name");
            table.AddColumn("Email");
            table.AddColumn("Role");
            table.AddColumn("Created");

            foreach (var user in users)
            {
                table.AddRow(
                    user.Id.ToString()[..8] + "...",
                    user.Name,
                    user.Email,
                    user.Role.ToString(),
                    user.CreatedAt.ToString("yyyy-MM-dd")
                );
            }

            AnsiConsole.Write(table);
            return 0;
        }
        catch (Exception ex)
        {
            AnsiConsole.WriteException(ex);
            return 1;
        }
    }
}

[Description("Create a new user")]
public class UserCreateCommand : AsyncCommand<UserCreateCommand.Settings>
{
    private readonly IUserService _userService;

    public UserCreateCommand(IUserService userService)
    {
        _userService = userService;
    }

    public class Settings : CommandSettings
    {
        [CommandArgument(0, "<EMAIL>")]
        [Description("User email address")]
        public string Email { get; set; } = string.Empty;

        [CommandArgument(1, "<NAME>")]
        [Description("User display name")]
        public string Name { get; set; } = string.Empty;

        [CommandOption("-r|--role")]
        [Description("User role (User, Admin, Moderator)")]
        [DefaultValue(UserRole.User)]
        public UserRole Role { get; set; } = UserRole.User;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, Settings settings)
    {
        try
        {
            // Check if user already exists
            var existingUser = await _userService.GetUserByEmailAsync(settings.Email);
            if (existingUser != null)
            {
                AnsiConsole.MarkupLine($"[red]User with email {settings.Email} already exists![/]");
                return 1;
            }

            var user = await _userService.CreateUserAsync(settings.Email, settings.Name, settings.Role);

            AnsiConsole.MarkupLine($"[green]Successfully created user:[/]");
            AnsiConsole.MarkupLine($"  ID: {user.Id}");
            AnsiConsole.MarkupLine($"  Name: {user.Name}");
            AnsiConsole.MarkupLine($"  Email: {user.Email}");
            AnsiConsole.MarkupLine($"  Role: {user.Role}");

            return 0;
        }
        catch (Exception ex)
        {
            AnsiConsole.WriteException(ex);
            return 1;
        }
    }
}

[Description("Search users by name or email")]
public class UserSearchCommand : AsyncCommand<UserSearchCommand.Settings>
{
    private readonly IUserService _userService;

    public UserSearchCommand(IUserService userService)
    {
        _userService = userService;
    }

    public class Settings : CommandSettings
    {
        [CommandArgument(0, "<SEARCH_TERM>")]
        [Description("Search term to find users")]
        public string SearchTerm { get; set; } = string.Empty;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, Settings settings)
    {
        try
        {
            var users = await _userService.SearchUsersAsync(settings.SearchTerm);

            if (!users.Any())
            {
                AnsiConsole.MarkupLine($"[yellow]No users found matching '{settings.SearchTerm}'[/]");
                return 0;
            }

            var table = new Table();
            table.AddColumn("ID");
            table.AddColumn("Name");
            table.AddColumn("Email");
            table.AddColumn("Role");

            foreach (var user in users)
            {
                table.AddRow(
                    user.Id.ToString()[..8] + "...",
                    user.Name,
                    user.Email,
                    user.Role.ToString()
                );
            }

            AnsiConsole.Write(table);
            return 0;
        }
        catch (Exception ex)
        {
            AnsiConsole.WriteException(ex);
            return 1;
        }
    }
}

[Description("Get user statistics")]
public class UserStatsCommand : AsyncCommand
{
    private readonly IUserService _userService;

    public UserStatsCommand(IUserService userService)
    {
        _userService = userService;
    }

    public override async Task<int> ExecuteAsync(CommandContext context)
    {
        try
        {
            var totalUsers = await _userService.GetUserCountAsync();
            var adminUsers = await _userService.GetUsersByRoleAsync(UserRole.Admin);
            var managerUsers = await _userService.GetUsersByRoleAsync(UserRole.Manager);
            var regularUsers = await _userService.GetUsersByRoleAsync(UserRole.User);

            var table = new Table();
            table.AddColumn("Metric");
            table.AddColumn("Count");

            table.AddRow("Total Users", totalUsers.ToString());
            table.AddRow("Admin Users", adminUsers.Count().ToString());
            table.AddRow("Manager Users", managerUsers.Count().ToString());
            table.AddRow("Regular Users", regularUsers.Count().ToString());

            AnsiConsole.Write(table);
            return 0;
        }
        catch (Exception ex)
        {
            AnsiConsole.WriteException(ex);
            return 1;
        }
    }
}
