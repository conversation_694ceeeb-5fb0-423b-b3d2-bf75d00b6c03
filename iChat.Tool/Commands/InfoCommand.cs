using Spectre.Console;
using Spectre.Console.Cli;

namespace iChat.Tool.Commands;

public class InfoSettings : CommandSettings
{
}

public class InfoCommand : Command<InfoSettings>
{
    public override int Execute(CommandContext context, InfoSettings settings)
    {
        // Create a nice header
        var rule = new Rule("[blue]iChat.Tool - Command Line Interface[/]");
        rule.Justification = Justify.Left;
        AnsiConsole.Write(rule);
        AnsiConsole.WriteLine();

        // Description panel
        var description = new Panel(
            "iChat.Tool is a command-line utility for administering and managing the iChat system.\n" +
            "It provides tools for database migrations, system maintenance, and configuration.")
            .Header("[yellow]DESCRIPTION[/]")
            .BorderColor(Color.Yellow);
        AnsiConsole.Write(description);
        AnsiConsole.WriteLine();

        // Usage
        AnsiConsole.MarkupLine("[yellow]USAGE:[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool[/] [green][[command]][/] [blue][[options]][/]");
        AnsiConsole.WriteLine();

        // Commands table
        var commandsTable = new Table();
        commandsTable.Title = new TableTitle("[yellow]COMMANDS[/]");
        commandsTable.AddColumn("Command");
        commandsTable.AddColumn("Description");

        commandsTable.AddRow("[green]version[/]", "Display the current version information");
        commandsTable.AddRow("[green]migrate[/]", "Run database migrations");
        commandsTable.AddRow("[green]migrate --check[/]", "Check database connection without running migrations");
        commandsTable.AddRow("[green]create-migration <name>[/]", "Create a new Entity Framework migration");
        commandsTable.AddRow("[green]document list[/]", "List all documents with their document store and section count");
        commandsTable.AddRow("[green]document create[/]", "Create a new document from a file");
        commandsTable.AddRow("[green]document store list[/]", "List all document stores");
        commandsTable.AddRow("[green]document store create[/]", "Create a new document store");
        commandsTable.AddRow("[green]user list[/]", "List all users in the system");
        commandsTable.AddRow("[green]user create[/]", "Create a new user");
        commandsTable.AddRow("[green]user search[/]", "Search users by name or email");
        commandsTable.AddRow("[green]user stats[/]", "Get user statistics");
        commandsTable.AddRow("[green]info[/]", "Display this help information");

        AnsiConsole.Write(commandsTable);
        AnsiConsole.WriteLine();

        // Global options
        var optionsTable = new Table();
        optionsTable.Title = new TableTitle("[yellow]GLOBAL OPTIONS[/]");
        optionsTable.AddColumn("Option");
        optionsTable.AddColumn("Description");

        optionsTable.AddRow("[blue]-h, --help[/]", "Show help and usage information");
        optionsTable.AddRow("[blue]--version[/]", "Show version information");

        AnsiConsole.Write(optionsTable);
        AnsiConsole.WriteLine();

        // Environment variables
        var envTable = new Table();
        envTable.Title = new TableTitle("[yellow]ENVIRONMENT VARIABLES[/]");
        envTable.AddColumn("Variable");
        envTable.AddColumn("Description");

        envTable.AddRow("[red]SUPABASE_URL[/]", "Supabase project URL (required for database operations)");
        envTable.AddRow("[red]SUPABASE_ANON_KEY[/]", "Supabase anonymous key (required for database operations)");
        envTable.AddRow("[red]CONNECTION_STRING[/]", "Database connection string (required for database operations)");

        AnsiConsole.Write(envTable);
        AnsiConsole.WriteLine();

        // Examples
        AnsiConsole.MarkupLine("[yellow]EXAMPLES:[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool version[/]                    [dim]# Show version information[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool migrate[/]                    [dim]# Run all pending migrations[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool migrate --check[/]            [dim]# Test database connection[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool create-migration InitialCreate[/] [dim]# Create initial migration[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document list[/]              [dim]# List all documents[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document create \"My Doc\" file.txt[/] [dim]# Create document from file[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool document store list[/]        [dim]# List document stores[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool user list[/]                  [dim]# List all users[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool <NAME_EMAIL> \"John Doe\"[/] [dim]# Create user[/]");
        AnsiConsole.MarkupLine("  [cyan]iChat.Tool info[/]                       [dim]# Show this help information[/]");
        AnsiConsole.WriteLine();

        // Configuration
        var configPanel = new Panel(
            "Configuration can be provided via:\n" +
            "• Environment variables\n" +
            "• appsettings.json file\n" +
            "• Command line options (where available)")
            .Header("[yellow]CONFIGURATION[/]")
            .BorderColor(Color.Yellow);
        AnsiConsole.Write(configPanel);
        AnsiConsole.WriteLine();

        AnsiConsole.MarkupLine("For more information, visit: [link]https://github.com/your-org/ichat[/]");

        return 0;
    }
}
