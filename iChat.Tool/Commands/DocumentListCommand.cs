using Spectre.Console;
using Spectre.Console.Cli;
using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Shared.Models;
using System.ComponentModel;

namespace iChat.Tool.Commands;

public class DocumentListSettings : CommandSettings
{
    [CommandOption("--store")]
    [Description("Filter documents by document store name")]
    public string? StoreName { get; set; }

    [CommandOption("--limit")]
    [Description("Maximum number of documents to display")]
    public int? Limit { get; set; }
}

public class DocumentListCommand : AsyncCommand<DocumentListSettings>
{
    private readonly IDocumentService _documentService;
    private readonly IDocumentStoreService _documentStoreService;
    private readonly ILogger<DocumentListCommand> _logger;

    public DocumentListCommand(
        IDocumentService documentService,
        IDocumentStoreService documentStoreService,
        ILogger<DocumentListCommand> logger)
    {
        _documentService = documentService;
        _documentStoreService = documentStoreService;
        _logger = logger;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, DocumentListSettings settings)
    {
        try
        {
            _logger.LogInformation("Fetching documents from database...");

            IEnumerable<Document> documents;

            // Apply store filter if specified
            if (!string.IsNullOrEmpty(settings.StoreName))
            {
                _logger.LogInformation($"Filtering by document store containing: {settings.StoreName}");

                // Find the document store first
                var stores = await _documentStoreService.SearchDocumentStoresAsync(settings.StoreName);
                var store = stores.FirstOrDefault();

                if (store == null)
                {
                    AnsiConsole.MarkupLine($"[yellow]No document store found containing: {settings.StoreName}[/]");
                    return 0;
                }

                documents = await _documentService.GetDocumentsByStoreAsync(store.Id);
            }
            else
            {
                documents = await _documentService.GetAllDocumentsAsync();
            }

            // Apply limit if specified
            if (settings.Limit.HasValue)
            {
                documents = documents.Take(settings.Limit.Value);
                _logger.LogInformation($"Limiting results to: {settings.Limit.Value}");
            }

            var documentList = documents.ToList();

            if (!documentList.Any())
            {
                AnsiConsole.MarkupLine("[yellow]No documents found.[/]");
                if (!string.IsNullOrEmpty(settings.StoreName))
                {
                    AnsiConsole.MarkupLine($"[dim]No documents found for store containing: {settings.StoreName}[/]");
                }
                return 0;
            }

            AnsiConsole.MarkupLine($"[green]Found {documentList.Count} document(s)[/]");

            // Create a table to display the documents
            var table = new Table();
            table.Title = new TableTitle("[yellow]DOCUMENTS[/]");
            table.AddColumn("ID");
            table.AddColumn("Title");
            table.AddColumn("Document Store");
            table.AddColumn("MIME Type");
            table.AddColumn("Created");
            table.AddColumn("Updated");

            // Get document store names for display
            var storeIds = documentList.Select(d => d.DocumentStoreId).Distinct().ToList();
            var storeNames = new Dictionary<Guid, string>();

            foreach (var storeId in storeIds)
            {
                var store = await _documentStoreService.GetDocumentStoreByIdAsync(storeId);
                storeNames[storeId] = store?.Name ?? "Unknown";
            }

            foreach (var document in documentList)
            {
                var storeName = storeNames.GetValueOrDefault(document.DocumentStoreId, "Unknown");
                table.AddRow(
                    document.Id.ToString()[..8] + "...",
                    Markup.Escape(document.Title),
                    Markup.Escape(storeName),
                    Markup.Escape(document.MimeType),
                    document.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                    document.UpdatedAt.ToString("yyyy-MM-dd HH:mm")
                );
            }

            AnsiConsole.Write(table);

            // Show summary statistics
            var uniqueStoresCount = documentList.Select(d => d.DocumentStoreId).Distinct().Count();

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine($"[dim]Summary: {documentList.Count} documents across {uniqueStoresCount} store(s)[/]");

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list documents");
            AnsiConsole.MarkupLine($"[red]❌ Failed to list documents: {ex.Message}[/]");
            return 1;
        }
    }
}