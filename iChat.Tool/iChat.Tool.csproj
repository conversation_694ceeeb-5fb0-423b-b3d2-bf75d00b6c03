<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Spectre.Console.Cli" Version="0.51.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.9" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.9" />
    <PackageReference Include="Supabase" Version="1.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.1-dev-02307" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.1-dev-00953" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.9" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.9" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../iChat.Shared/iChat.Shared.csproj" />
    <ProjectReference Include="../iChat.Services/iChat.Services.csproj" />
  </ItemGroup>

</Project>
