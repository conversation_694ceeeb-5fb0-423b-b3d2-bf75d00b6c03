using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using iChat.Shared.Data;
using Supabase;

namespace iChat.Tool.Services;

public class MigrationService : IMigrationService
{
    private readonly ILogger<MigrationService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ApplicationDbContext _dbContext;

    public MigrationService(ILogger<MigrationService> logger, IConfiguration configuration, ApplicationDbContext dbContext)
    {
        _logger = logger;
        _configuration = configuration;
        _dbContext = dbContext;
    }

    public async Task<bool> CheckConnectionAsync()
    {
        try
        {
            // Check EF Core database connection
            var canConnect = await _dbContext.Database.CanConnectAsync();
            if (canConnect)
            {
                _logger.LogInformation("Successfully connected to database via Entity Framework");
                return true;
            }
            else
            {
                _logger.LogError("Cannot connect to database via Entity Framework");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to database");
            return false;
        }
    }

    public async Task RunMigrationsAsync()
    {
        _logger.LogInformation("Starting database migrations...");

        var connected = await CheckConnectionAsync();
        if (!connected)
        {
            throw new InvalidOperationException("Cannot connect to database. Please check your configuration.");
        }

        try
        {
            _logger.LogInformation("Applying Entity Framework migrations...");

            // Check if database exists and if __EFMigrationsHistory table exists
            var databaseExists = await _dbContext.Database.CanConnectAsync();
            if (!databaseExists)
            {
                _logger.LogInformation("Database does not exist. Creating database...");
                await _dbContext.Database.EnsureCreatedAsync();
            }

            // Check if this is a fresh database (no migrations history)
            try
            {
                var appliedMigrations = await _dbContext.Database.GetAppliedMigrationsAsync();
                _logger.LogInformation($"Found {appliedMigrations.Count()} previously applied migrations");
            }
            catch (Exception ex) when (ex.Message.Contains("__EFMigrationsHistory") || ex.Message.Contains("does not exist"))
            {
                _logger.LogInformation("No migration history found. This appears to be a fresh database.");
            }

            // Get pending migrations
            IEnumerable<string> pendingMigrations;
            try
            {
                pendingMigrations = await _dbContext.Database.GetPendingMigrationsAsync();
            }
            catch (Exception ex) when (ex.Message.Contains("__EFMigrationsHistory") || ex.Message.Contains("does not exist"))
            {
                // If we can't get pending migrations due to missing history table,
                // we need to ensure the database is created and then migrate
                _logger.LogInformation("Unable to query migration history. Ensuring database is created and applying all migrations...");
                await _dbContext.Database.MigrateAsync();
                _logger.LogInformation("Database created and all migrations applied successfully");
                return;
            }

            if (pendingMigrations.Any())
            {
                _logger.LogInformation($"Applying {pendingMigrations.Count()} pending migrations...");
                foreach (var migration in pendingMigrations)
                {
                    _logger.LogInformation($"  - {migration}");
                }

                await _dbContext.Database.MigrateAsync();
                _logger.LogInformation("All migrations applied successfully");
            }
            else
            {
                _logger.LogInformation("No pending migrations found. Database is up to date.");
            }

            _logger.LogInformation("Database migrations completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Migration failed");
            throw;
        }
    }
}
