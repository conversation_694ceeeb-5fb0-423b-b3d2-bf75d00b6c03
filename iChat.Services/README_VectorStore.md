# Postgres Vector Store Integration

This document describes the integration of Microsoft Semantic Kernel's Postgres Vector Store connector into iChat.Services for efficient vector similarity search.

## Overview

The integration provides:
- **Efficient Vector Search**: Uses pgvector extension for fast similarity search
- **Dual Storage**: Maintains both traditional EF Core database and vector store
- **Seamless Integration**: Transparent to existing code using IEmbeddingService
- **Flexible Configuration**: Configurable through appsettings.json

## Architecture

### Components

1. **DocumentSectionVector**: Vector store model optimized for similarity search
2. **IVectorStoreService**: Service interface for vector operations
3. **VectorStoreService**: Implementation using Postgres Vector Store connector
4. **EnhancedEmbeddingService**: Coordinates between traditional DB and vector store
5. **VectorStoreOptions**: Configuration options

### Data Flow

```
Document Upload → Text Chunking → Embedding Generation → Dual Storage
                                                        ├─ EF Core (DocumentSection)
                                                        └─ Vector Store (DocumentSectionVector)

Search Query → Embedding Generation → Vector Search → Results Conversion → DocumentSection[]
```

## Setup

### 1. Database Prerequisites

Ensure your Postgres database has the pgvector extension:

```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

### 2. Configuration

Add to your `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Username=postgres;Password=your_password;Database=ichat;"
  },
  "VectorStore": {
    "UseSameConnectionAsMainDb": true,
    "CollectionName": "document_sections_vector",
    "EmbeddingDimensions": 1536,
    "DefaultSimilarityThreshold": 0.7,
    "DefaultMaxResults": 10,
    "AutoInitialize": true
  }
}
```

### 3. Service Registration

#### Option A: Simple Registration (Recommended)
```csharp
services.AddIChatServicesWithVectorStore(configuration);
```

#### Option B: Manual Registration
```csharp
services.AddIChatServices(connectionString);
// Vector store is automatically included
```

#### Option C: Custom Configuration
```csharp
services.AddIChatServices(
    options => options.UseNpgsql(connectionString),
    vectorStoreConnectionString);
```

### 4. Initialization

The vector store collection is automatically initialized if `AutoInitialize` is true. For manual initialization:

```csharp
public async Task InitializeAsync(IServiceProvider serviceProvider)
{
    var embeddingService = serviceProvider.GetRequiredService<IEmbeddingService>();
    if (embeddingService is EnhancedEmbeddingService enhanced)
    {
        await enhanced.InitializeVectorStoreAsync();
    }
}
```

## Usage

### Basic Search

The existing `IEmbeddingService` interface works transparently:

```csharp
// Text-based search (generates embedding automatically)
var results = await embeddingService.SearchSimilarSectionsAsync(
    "What is machine learning?", 
    maxResults: 5, 
    similarityThreshold: 0.8);

// Embedding-based search
var embedding = await embeddingService.GenerateEmbeddingAsync("query text");
var results = await embeddingService.SearchSimilarSectionsAsync(
    embedding, 
    maxResults: 5, 
    similarityThreshold: 0.8);
```

### Document Store Filtering

Search within a specific document store:

```csharp
if (embeddingService is EnhancedEmbeddingService enhanced)
{
    var results = await enhanced.SearchSimilarSectionsInStoreAsync(
        "query text",
        documentStoreId,
        maxResults: 10,
        similarityThreshold: 0.7);
}
```

### Document Management

Creating and deleting document sections automatically manages both stores:

```csharp
// Creates in both EF Core and vector store
var section = await embeddingService.CreateDocumentSectionAsync(
    documentId, content, sectionIndex, tokenCount);

// Deletes from both stores
await embeddingService.DeleteDocumentSectionsAsync(documentId);
```

## Performance Considerations

### Indexing

The vector store automatically creates HNSW indexes for efficient similarity search:

```sql
-- Automatically created by the connector
CREATE INDEX ON document_sections_vector USING hnsw (embedding vector_cosine_ops);
```

### Batch Operations

For bulk operations, use the vector store service directly:

```csharp
var vectorSections = documentSections.Select(ds => 
    DocumentSectionVector.FromDocumentSection(ds, documentTitle, documentStoreId));

await vectorStoreService.UpsertDocumentSectionsAsync(vectorSections);
```

### Memory Usage

- Vector embeddings use `ReadOnlyMemory<float>` for efficient memory management
- Batch operations are recommended for large datasets
- Consider pagination for large result sets

## Migration from Existing Setup

### 1. Backup Existing Data

```sql
-- Backup existing document sections
CREATE TABLE document_sections_backup AS SELECT * FROM "DocumentSections";
```

### 2. Populate Vector Store

Create a migration service to populate the vector store with existing data:

```csharp
public async Task MigrateExistingDataAsync()
{
    var existingSections = await documentSectionRepository.GetAllAsync();
    
    foreach (var section in existingSections)
    {
        var document = await documentService.GetDocumentByIdAsync(section.DocumentId);
        var vectorSection = DocumentSectionVector.FromDocumentSection(
            section, document.Title, document.DocumentStoreId);
        
        await vectorStoreService.UpsertDocumentSectionAsync(vectorSection);
    }
}
```

## Troubleshooting

### Common Issues

1. **pgvector Extension Missing**
   ```
   Error: relation "vector" does not exist
   Solution: Install pgvector extension in your database
   ```

2. **Connection String Issues**
   ```
   Error: UseVector() not called on NpgsqlDataSourceBuilder
   Solution: The connector automatically handles this when using AddPostgresVectorStore()
   ```

3. **Dimension Mismatch**
   ```
   Error: Vector dimension mismatch
   Solution: Ensure EmbeddingDimensions in config matches your embedding model
   ```

### Monitoring

Enable detailed logging:

```json
{
  "Logging": {
    "LogLevel": {
      "iChat.Services.Services.VectorStoreService": "Debug",
      "iChat.Services.Services.EnhancedEmbeddingService": "Information"
    }
  }
}
```

## Advanced Configuration

### Custom Distance Functions

Modify the `DocumentSectionVector` model to use different distance functions:

```csharp
[VectorStoreVector(
    Dimensions: 1536, 
    DistanceFunction = DistanceFunction.EuclideanDistance, // or DotProductSimilarity
    StorageName = "embedding")]
public ReadOnlyMemory<float>? Embedding { get; set; }
```

### Multiple Vector Collections

Create separate collections for different types of content:

```csharp
services.AddScoped<IVectorStoreService>(provider =>
{
    var vectorStore = provider.GetRequiredService<PostgresVectorStore>();
    return new VectorStoreService(vectorStore, "custom_collection_name");
});
```

## Best Practices

1. **Use Batch Operations**: For bulk inserts/updates
2. **Monitor Performance**: Track search latency and accuracy
3. **Tune Similarity Thresholds**: Based on your use case
4. **Regular Maintenance**: Monitor vector store size and performance
5. **Backup Strategy**: Include both traditional DB and vector store in backups

## Future Enhancements

- Hybrid search combining vector and text search
- Multiple embedding models support
- Automatic re-indexing on model changes
- Advanced filtering capabilities
- Performance analytics and monitoring
