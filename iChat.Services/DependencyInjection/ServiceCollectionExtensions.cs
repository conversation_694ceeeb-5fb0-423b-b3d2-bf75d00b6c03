using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.SemanticKernel.Connectors.PgVector;
using Npgsql;
using iChat.Services.Interfaces;
using iChat.Services.Services;
using iChat.Services.Repositories;
using iChat.Services.Configuration;
using iChat.Shared.Data;

namespace iChat.Services.DependencyInjection;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddIChatServices(this IServiceCollection services, string connectionString)
    {
        // Add DbContext
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(connectionString));

        // Add Postgres Vector Store
        services.AddPostgresVectorStore(connectionString);

        // Add Repositories
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Add Services
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IDocumentService, DocumentService>();
        services.AddScoped<IChatSessionService, ChatSessionService>();
        services.AddScoped<IMessageService, MessageService>();
        services.AddScoped<IDocumentStoreService, DocumentStoreService>();
        services.AddScoped<IEscalatedQueryService, EscalatedQueryService>();
        services.AddScoped<IEmbeddingService, EmbeddingService>();
        services.AddScoped<IVectorStoreService, VectorStoreService>();

        return services;
    }

    public static IServiceCollection AddIChatServices(this IServiceCollection services, Action<DbContextOptionsBuilder> configureDbContext)
    {
        // Add DbContext with custom configuration
        services.AddDbContext<ApplicationDbContext>(configureDbContext);

        // Note: When using custom DbContext configuration, you need to separately configure the vector store
        // Call AddPostgresVectorStore with your connection string or configure NpgsqlDataSource manually

        // Add Repositories
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Add Services
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IDocumentService, DocumentService>();
        services.AddScoped<IChatSessionService, ChatSessionService>();
        services.AddScoped<IMessageService, MessageService>();
        services.AddScoped<IDocumentStoreService, DocumentStoreService>();
        services.AddScoped<IEscalatedQueryService, EscalatedQueryService>();
        services.AddScoped<IEmbeddingService, EmbeddingService>();
        services.AddScoped<IVectorStoreService, VectorStoreService>();

        return services;
    }

    /// <summary>
    /// Add iChat services with custom vector store configuration
    /// Use this when you need to configure the vector store separately from EF Core
    /// </summary>
    public static IServiceCollection AddIChatServices(
        this IServiceCollection services,
        Action<DbContextOptionsBuilder> configureDbContext,
        string vectorStoreConnectionString)
    {
        // Add DbContext with custom configuration
        services.AddDbContext<ApplicationDbContext>(configureDbContext);

        // Add Postgres Vector Store with separate connection string
        services.AddPostgresVectorStore(vectorStoreConnectionString);

        // Add Repositories
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Add Services
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IDocumentService, DocumentService>();
        services.AddScoped<IChatSessionService, ChatSessionService>();
        services.AddScoped<IMessageService, MessageService>();
        services.AddScoped<IDocumentStoreService, DocumentStoreService>();
        services.AddScoped<IEscalatedQueryService, EscalatedQueryService>();
        services.AddScoped<IEmbeddingService, EmbeddingService>();
        services.AddScoped<IVectorStoreService, VectorStoreService>();

        return services;
    }

    /// <summary>
    /// Add iChat services with vector store configuration from IConfiguration
    /// This is the recommended approach for production applications
    /// </summary>
    public static IServiceCollection AddIChatServicesWithVectorStore(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("DefaultConnection connection string is required");

        // Configure vector store options
        var vectorStoreOptions = new VectorStoreOptions();
        configuration.GetSection(VectorStoreOptions.SectionName).Bind(vectorStoreOptions);
        services.Configure<VectorStoreOptions>(configuration.GetSection(VectorStoreOptions.SectionName));

        // Use same connection string for vector store if configured to do so
        var vectorStoreConnectionString = vectorStoreOptions.UseSameConnectionAsMainDb
            ? connectionString
            : vectorStoreOptions.ConnectionString;

        if (string.IsNullOrEmpty(vectorStoreConnectionString))
        {
            throw new InvalidOperationException("Vector store connection string is required");
        }

        // Add DbContext
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(connectionString));

        // Add Postgres Vector Store
        services.AddPostgresVectorStore(vectorStoreConnectionString);

        // Add Repositories
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Add base services
        services.AddScoped<EmbeddingService>(); // Register the base service
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IDocumentService, DocumentService>();
        services.AddScoped<IChatSessionService, ChatSessionService>();
        services.AddScoped<IMessageService, MessageService>();
        services.AddScoped<IDocumentStoreService, DocumentStoreService>();
        services.AddScoped<IEscalatedQueryService, EscalatedQueryService>();
        services.AddScoped<IVectorStoreService, VectorStoreService>();

        // Register the enhanced embedding service as the main IEmbeddingService
        services.AddScoped<IEmbeddingService, EnhancedEmbeddingService>();

        return services;
    }
}
