using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using iChat.Services.Interfaces;
using iChat.Services.Services;
using iChat.Services.Repositories;
using iChat.Shared.Data;

namespace iChat.Services.DependencyInjection;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddIChatServices(this IServiceCollection services, string connectionString)
    {
        // Add DbContext
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(connectionString));

        // Add Repositories
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Add Services
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IDocumentService, DocumentService>();
        services.AddScoped<IChatSessionService, ChatSessionService>();
        services.AddScoped<IMessageService, MessageService>();
        services.AddScoped<IDocumentStoreService, DocumentStoreService>();
        services.AddScoped<IEscalatedQueryService, EscalatedQueryService>();
        services.AddScoped<IEmbeddingService, EmbeddingService>();

        return services;
    }

    public static IServiceCollection AddIChatServices(this IServiceCollection services, Action<DbContextOptionsBuilder> configureDbContext)
    {
        // Add DbContext with custom configuration
        services.AddDbContext<ApplicationDbContext>(configureDbContext);

        // Add Repositories
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Add Services
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IDocumentService, DocumentService>();
        services.AddScoped<IChatSessionService, ChatSessionService>();
        services.AddScoped<IMessageService, MessageService>();
        services.AddScoped<IDocumentStoreService, DocumentStoreService>();
        services.AddScoped<IEscalatedQueryService, EscalatedQueryService>();
        services.AddScoped<IEmbeddingService, EmbeddingService>();

        return services;
    }
}
