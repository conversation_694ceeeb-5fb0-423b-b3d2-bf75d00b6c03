# iChat.Services

This project contains the shared business logic layer for the iChat application. It provides a clean separation between data access and business logic, making it reusable across multiple projects (iChat.Api, iChat.Tool, iChat.Service).

## Architecture

The services layer follows the Repository pattern and Dependency Injection principles:

```
iChat.Shared (Data models, DTOs, Enums)
    ↓
iChat.Services (Business logic, repositories, services)
    ↓
iChat.Api + iChat.Tool + iChat.Service (Consumers)
```

## Project Structure

```
iChat.Services/
├── Interfaces/           # Service interfaces
│   ├── IUserService.cs
│   ├── IDocumentService.cs
│   ├── IChatSessionService.cs
│   ├── IMessageService.cs
│   ├── IDocumentStoreService.cs
│   ├── IEmbeddingService.cs
│   └── IEscalatedQueryService.cs
├── Services/             # Service implementations
│   ├── UserService.cs
│   ├── DocumentService.cs
│   ├── ChatSessionService.cs
│   ├── MessageService.cs
│   ├── DocumentStoreService.cs
│   └── EscalatedQueryService.cs
├── Repositories/         # Generic repository pattern
│   ├── IRepository.cs
│   └── Repository.cs
└── DependencyInjection/  # Service registration
    └── ServiceCollectionExtensions.cs
```

## Services Overview

### Core Services

- **IUserService**: User management operations (CRUD, search, role management)
- **IDocumentService**: Document upload, processing, and management
- **IChatSessionService**: Chat session lifecycle management
- **IMessageService**: Message handling, escalation, and search
- **IDocumentStoreService**: Document store management and organization
- **IEscalatedQueryService**: Escalated query tracking and resolution

### Repository Layer

- **IRepository<T>**: Generic repository interface with common CRUD operations
- **Repository<T>**: Base implementation using Entity Framework Core

### Embedding Service (To Be Implemented)

- **IEmbeddingService**: Vector embeddings for RAG functionality
  - Text chunking and tokenization
  - Embedding generation (OpenAI, Azure OpenAI, etc.)
  - Similarity search and document section management

## Usage

### 1. Register Services in DI Container

```csharp
// With connection string
services.AddIChatServices(connectionString);

// With custom DbContext configuration
services.AddIChatServices(options => 
    options.UseNpgsql(connectionString));
```

### 2. Inject Services in Controllers/Commands

```csharp
public class DocumentController : ControllerBase
{
    private readonly IDocumentService _documentService;
    private readonly IUserService _userService;

    public DocumentController(
        IDocumentService documentService,
        IUserService userService)
    {
        _documentService = documentService;
        _userService = userService;
    }

    [HttpPost]
    public async Task<IActionResult> UploadDocument(UploadDocumentRequest request)
    {
        var document = await _documentService.UploadDocumentAsync(
            request.Title,
            request.Content,
            request.FilePath,
            request.MimeType,
            request.UserId,
            request.DocumentStoreId);

        return Ok(document);
    }
}
```

### 3. CLI Tool Usage

```csharp
public class DocumentCommand : AsyncCommand<DocumentCommand.Settings>
{
    private readonly IDocumentService _documentService;

    public DocumentCommand(IDocumentService documentService)
    {
        _documentService = documentService;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, Settings settings)
    {
        var documents = await _documentService.GetAllDocumentsAsync();
        // Display documents...
        return 0;
    }
}
```

## Dependencies

- **Microsoft.EntityFrameworkCore**: Data access
- **Microsoft.Extensions.DependencyInjection**: Dependency injection
- **Microsoft.Extensions.Logging**: Logging
- **Supabase**: Database and authentication
- **iChat.Shared**: Data models and shared types

## Next Steps

1. **Implement IEmbeddingService**: Choose LLM provider (OpenAI, Azure OpenAI, etc.) and implement embedding functionality
2. **Add Caching**: Implement caching layer for frequently accessed data
3. **Add Validation**: Add input validation and business rule validation
4. **Add Unit Tests**: Create comprehensive test suite for all services
5. **Add Background Services**: Implement background processing for document indexing

## Notes

- All services include comprehensive error handling and logging
- Services follow async/await patterns for scalability
- Repository pattern provides abstraction over Entity Framework
- Services are designed to be stateless and thread-safe
- Business logic is separated from data access concerns
