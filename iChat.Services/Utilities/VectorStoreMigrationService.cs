using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Models;
using iChat.Services.Repositories;
using iChat.Shared.Models;

namespace iChat.Services.Utilities;

/// <summary>
/// Utility service for migrating existing document sections to the vector store
/// </summary>
public class VectorStoreMigrationService
{
    private readonly IVectorStoreService _vectorStoreService;
    private readonly IDocumentService _documentService;
    private readonly IRepository<DocumentSection> _documentSectionRepository;
    private readonly ILogger<VectorStoreMigrationService> _logger;

    public VectorStoreMigrationService(
        IVectorStoreService vectorStoreService,
        IDocumentService documentService,
        IRepository<DocumentSection> documentSectionRepository,
        ILogger<VectorStoreMigrationService> logger)
    {
        _vectorStoreService = vectorStoreService;
        _documentService = documentService;
        _documentSectionRepository = documentSectionRepository;
        _logger = logger;
    }

    /// <summary>
    /// Migrate all existing document sections to the vector store
    /// </summary>
    public async Task<MigrationResult> MigrateAllDocumentSectionsAsync(bool skipExisting = true)
    {
        var result = new MigrationResult();
        
        try
        {
            _logger.LogInformation("Starting migration of document sections to vector store");

            // Initialize vector store if needed
            await _vectorStoreService.InitializeAsync();

            // Get all existing document sections
            var allSections = await _documentSectionRepository.GetAllAsync();
            result.TotalSections = allSections.Count();

            _logger.LogInformation("Found {Count} document sections to migrate", result.TotalSections);

            // Group by document for efficient processing
            var sectionsByDocument = allSections.GroupBy(s => s.DocumentId);

            foreach (var documentGroup in sectionsByDocument)
            {
                try
                {
                    await MigrateDocumentSectionsAsync(documentGroup.Key, documentGroup, result, skipExisting);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to migrate sections for document: {DocumentId}", documentGroup.Key);
                    result.FailedDocuments.Add(documentGroup.Key);
                    result.ErrorMessages.Add($"Document {documentGroup.Key}: {ex.Message}");
                }
            }

            result.Success = result.FailedSections == 0 && result.FailedDocuments.Count == 0;
            
            _logger.LogInformation("Migration completed. Success: {Success}, Migrated: {Migrated}, Skipped: {Skipped}, Failed: {Failed}",
                result.Success, result.MigratedSections, result.SkippedSections, result.FailedSections);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Migration failed with exception");
            result.Success = false;
            result.ErrorMessages.Add($"Migration failed: {ex.Message}");
            return result;
        }
    }

    /// <summary>
    /// Migrate document sections for a specific document
    /// </summary>
    public async Task<MigrationResult> MigrateDocumentSectionsAsync(Guid documentId, bool skipExisting = true)
    {
        var result = new MigrationResult();
        
        try
        {
            var sections = await _documentSectionRepository.FindAsync(s => s.DocumentId == documentId);
            result.TotalSections = sections.Count();

            if (result.TotalSections == 0)
            {
                _logger.LogInformation("No sections found for document: {DocumentId}", documentId);
                result.Success = true;
                return result;
            }

            await MigrateDocumentSectionsAsync(documentId, sections, result, skipExisting);
            result.Success = result.FailedSections == 0;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to migrate sections for document: {DocumentId}", documentId);
            result.Success = false;
            result.ErrorMessages.Add($"Document {documentId}: {ex.Message}");
            return result;
        }
    }

    private async Task MigrateDocumentSectionsAsync(
        Guid documentId, 
        IEnumerable<DocumentSection> sections, 
        MigrationResult result, 
        bool skipExisting)
    {
        // Get document metadata
        var document = await _documentService.GetDocumentByIdAsync(documentId);
        if (document == null)
        {
            _logger.LogWarning("Document not found: {DocumentId}, skipping sections", documentId);
            result.FailedDocuments.Add(documentId);
            result.ErrorMessages.Add($"Document {documentId} not found");
            return;
        }

        var vectorSections = new List<DocumentSectionVector>();

        foreach (var section in sections)
        {
            try
            {
                // Check if section already exists in vector store
                if (skipExisting)
                {
                    var existingVector = await _vectorStoreService.GetDocumentSectionAsync(section.Id);
                    if (existingVector != null)
                    {
                        result.SkippedSections++;
                        _logger.LogDebug("Skipping existing section: {SectionId}", section.Id);
                        continue;
                    }
                }

                // Validate embedding
                if (section.Embedding == null || section.Embedding.Length == 0)
                {
                    _logger.LogWarning("Section {SectionId} has no embedding, skipping", section.Id);
                    result.SkippedSections++;
                    continue;
                }

                // Convert to vector store model
                var vectorSection = DocumentSectionVector.FromDocumentSection(
                    section, 
                    document.Title, 
                    document.DocumentStoreId);

                vectorSections.Add(vectorSection);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to prepare section for migration: {SectionId}", section.Id);
                result.FailedSections++;
                result.ErrorMessages.Add($"Section {section.Id}: {ex.Message}");
            }
        }

        // Batch upsert to vector store
        if (vectorSections.Any())
        {
            try
            {
                await _vectorStoreService.UpsertDocumentSectionsAsync(vectorSections);
                result.MigratedSections += vectorSections.Count;
                
                _logger.LogInformation("Migrated {Count} sections for document: {DocumentId} - {Title}", 
                    vectorSections.Count, documentId, document.Title);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to batch upsert sections for document: {DocumentId}", documentId);
                result.FailedSections += vectorSections.Count;
                result.ErrorMessages.Add($"Batch upsert failed for document {documentId}: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Verify migration by comparing counts
    /// </summary>
    public async Task<VerificationResult> VerifyMigrationAsync()
    {
        try
        {
            var dbSectionCount = await _documentSectionRepository.CountAsync();
            var vectorSectionCount = await _vectorStoreService.GetDocumentSectionCountAsync();

            return new VerificationResult
            {
                DatabaseSectionCount = dbSectionCount,
                VectorStoreSectionCount = vectorSectionCount,
                IsConsistent = dbSectionCount == vectorSectionCount,
                Difference = Math.Abs(dbSectionCount - vectorSectionCount)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify migration");
            return new VerificationResult
            {
                IsConsistent = false,
                ErrorMessage = ex.Message
            };
        }
    }
}

/// <summary>
/// Result of a migration operation
/// </summary>
public class MigrationResult
{
    public bool Success { get; set; }
    public int TotalSections { get; set; }
    public int MigratedSections { get; set; }
    public int SkippedSections { get; set; }
    public int FailedSections { get; set; }
    public List<Guid> FailedDocuments { get; set; } = new();
    public List<string> ErrorMessages { get; set; } = new();

    public override string ToString()
    {
        return $"Migration Result - Success: {Success}, Total: {TotalSections}, " +
               $"Migrated: {MigratedSections}, Skipped: {SkippedSections}, Failed: {FailedSections}";
    }
}

/// <summary>
/// Result of a migration verification
/// </summary>
public class VerificationResult
{
    public int DatabaseSectionCount { get; set; }
    public int VectorStoreSectionCount { get; set; }
    public bool IsConsistent { get; set; }
    public int Difference { get; set; }
    public string? ErrorMessage { get; set; }

    public override string ToString()
    {
        if (!string.IsNullOrEmpty(ErrorMessage))
            return $"Verification failed: {ErrorMessage}";

        return $"DB: {DatabaseSectionCount}, Vector Store: {VectorStoreSectionCount}, " +
               $"Consistent: {IsConsistent}, Difference: {Difference}";
    }
}
