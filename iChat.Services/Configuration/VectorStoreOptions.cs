namespace iChat.Services.Configuration;

/// <summary>
/// Configuration options for the Postgres Vector Store
/// </summary>
public class VectorStoreOptions
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "VectorStore";

    /// <summary>
    /// Connection string for the vector store (can be same as main database)
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Name of the vector collection/table
    /// </summary>
    public string CollectionName { get; set; } = "document_sections_vector";

    /// <summary>
    /// Vector embedding dimensions (default for OpenAI text-embedding-ada-002)
    /// </summary>
    public int EmbeddingDimensions { get; set; } = 1536;

    /// <summary>
    /// Default similarity threshold for searches
    /// </summary>
    public double DefaultSimilarityThreshold { get; set; } = 0.7;

    /// <summary>
    /// Default maximum results for similarity searches
    /// </summary>
    public int DefaultMaxResults { get; set; } = 10;

    /// <summary>
    /// Whether to automatically initialize the vector store on startup
    /// </summary>
    public bool AutoInitialize { get; set; } = true;

    /// <summary>
    /// Whether to use the same connection string as the main database
    /// </summary>
    public bool UseSameConnectionAsMainDb { get; set; } = true;
}
