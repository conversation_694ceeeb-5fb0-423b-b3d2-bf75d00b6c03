using Microsoft.Extensions.VectorData;
using System.Text.Json.Serialization;

namespace iChat.Services.Models;

/// <summary>
/// Vector store model for document sections optimized for semantic search
/// This model is specifically designed for the Postgres Vector Store connector
/// </summary>
public class DocumentSectionVector
{
    /// <summary>
    /// Primary key for the vector store record
    /// </summary>
    [VectorStoreKey]
    public Guid Id { get; set; }

    /// <summary>
    /// Reference to the original document
    /// </summary>
    [VectorStoreData(StorageName = "document_id")]
    public Guid DocumentId { get; set; }

    /// <summary>
    /// The text content of the document section
    /// </summary>
    [VectorStoreData(StorageName = "content")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Section index within the document
    /// </summary>
    [VectorStoreData(StorageName = "section_index")]
    public int SectionIndex { get; set; }

    /// <summary>
    /// Token count for the section
    /// </summary>
    [VectorStoreData(StorageName = "token_count")]
    public int TokenCount { get; set; }

    /// <summary>
    /// Vector embedding for semantic search
    /// Using ReadOnlyMemory<float> as recommended by the connector
    /// </summary>
    [VectorStoreVector(Dimensions: 1536, DistanceFunction = DistanceFunction.CosineDistance, StorageName = "embedding")]
    public ReadOnlyMemory<float>? Embedding { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    [VectorStoreData(StorageName = "created_at")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Document title for context (denormalized for search efficiency)
    /// </summary>
    [VectorStoreData(StorageName = "document_title")]
    public string DocumentTitle { get; set; } = string.Empty;

    /// <summary>
    /// Document store ID for filtering
    /// </summary>
    [VectorStoreData(StorageName = "document_store_id")]
    public Guid DocumentStoreId { get; set; }

    /// <summary>
    /// Convert from DocumentSection entity to vector store model
    /// </summary>
    public static DocumentSectionVector FromDocumentSection(iChat.Shared.Models.DocumentSection section, string documentTitle, Guid documentStoreId)
    {
        return new DocumentSectionVector
        {
            Id = section.Id,
            DocumentId = section.DocumentId,
            Content = section.Content,
            SectionIndex = section.SectionIndex,
            TokenCount = section.TokenCount,
            Embedding = section.Embedding?.Length > 0 ? new ReadOnlyMemory<float>(section.Embedding) : null,
            CreatedAt = section.CreatedAt,
            DocumentTitle = documentTitle,
            DocumentStoreId = documentStoreId
        };
    }

    /// <summary>
    /// Convert to DocumentSection entity (for compatibility with existing code)
    /// </summary>
    public iChat.Shared.Models.DocumentSection ToDocumentSection()
    {
        return new iChat.Shared.Models.DocumentSection
        {
            Id = Id,
            DocumentId = DocumentId,
            Content = Content,
            SectionIndex = SectionIndex,
            TokenCount = TokenCount,
            Embedding = Embedding?.ToArray() ?? Array.Empty<float>(),
            CreatedAt = CreatedAt
        };
    }
}
