using Microsoft.Extensions.Logging;
using Microsoft.Extensions.VectorData;
using Microsoft.SemanticKernel.Connectors.PgVector;
using iChat.Services.Interfaces;
using iChat.Services.Models;
using Npgsql;

namespace iChat.Services.Services;

/// <summary>
/// Service for managing vector store operations using Postgres Vector Store
/// This service provides efficient vector similarity search capabilities
/// </summary>
public class VectorStoreService : IVectorStoreService
{
    private readonly IVectorStoreCollection<Guid, DocumentSectionVector> _collection;
    private readonly IEmbeddingService _embeddingService;
    private readonly ILogger<VectorStoreService> _logger;
    private readonly string _collectionName = "document_sections_vector";

    public VectorStoreService(
        PostgresVectorStore vectorStore,
        IEmbeddingService embeddingService,
        ILogger<VectorStoreService> logger)
    {
        _collection = vectorStore.GetCollection<Guid, DocumentSectionVector>(_collectionName);
        _embeddingService = embeddingService;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        try
        {
            // Create the collection if it doesn't exist
            await _collection.CreateCollectionIfNotExistsAsync();
            _logger.LogInformation("Vector store collection '{CollectionName}' initialized successfully", _collectionName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize vector store collection '{CollectionName}'", _collectionName);
            throw;
        }
    }

    public async Task<DocumentSectionVector> UpsertDocumentSectionAsync(DocumentSectionVector sectionVector)
    {
        try
        {
            var result = await _collection.UpsertAsync(sectionVector);
            _logger.LogDebug("Upserted document section vector: {SectionId}", sectionVector.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upsert document section vector: {SectionId}", sectionVector.Id);
            throw;
        }
    }

    public async Task<IEnumerable<DocumentSectionVector>> UpsertDocumentSectionsAsync(IEnumerable<DocumentSectionVector> sectionVectors)
    {
        try
        {
            var results = await _collection.UpsertBatchAsync(sectionVectors);
            _logger.LogInformation("Upserted {Count} document section vectors", sectionVectors.Count());
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upsert batch of document section vectors");
            throw;
        }
    }

    public async Task<IEnumerable<DocumentSectionVector>> SearchSimilarSectionsAsync(
        ReadOnlyMemory<float> queryEmbedding, 
        int maxResults = 10, 
        double similarityThreshold = 0.7,
        Guid? documentStoreId = null)
    {
        try
        {
            var searchOptions = new VectorSearchOptions
            {
                Top = maxResults,
                VectorPropertyName = "Embedding"
            };

            // Add filter for document store if specified
            if (documentStoreId.HasValue)
            {
                searchOptions.Filter = new VectorSearchFilter().EqualTo("DocumentStoreId", documentStoreId.Value);
            }

            var searchResults = await _collection.VectorizedSearchAsync(queryEmbedding, searchOptions);
            
            // Filter by similarity threshold
            var filteredResults = searchResults
                .Where(result => result.Score >= similarityThreshold)
                .Select(result => result.Record)
                .ToList();

            _logger.LogDebug("Vector search returned {Count} results above threshold {Threshold}", 
                filteredResults.Count, similarityThreshold);

            return filteredResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform vector similarity search");
            throw;
        }
    }

    public async Task<IEnumerable<DocumentSectionVector>> SearchSimilarSectionsAsync(
        string query, 
        int maxResults = 10, 
        double similarityThreshold = 0.7,
        Guid? documentStoreId = null)
    {
        try
        {
            // Generate embedding for the query
            var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(query);
            var queryEmbeddingMemory = new ReadOnlyMemory<float>(queryEmbedding);

            return await SearchSimilarSectionsAsync(queryEmbeddingMemory, maxResults, similarityThreshold, documentStoreId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform text-based vector similarity search for query: {Query}", query);
            throw;
        }
    }

    public async Task<IEnumerable<DocumentSectionVector>> GetDocumentSectionsAsync(Guid documentId)
    {
        try
        {
            var filter = new VectorSearchFilter().EqualTo("DocumentId", documentId);
            var searchOptions = new VectorSearchOptions
            {
                Filter = filter
            };

            // Use a dummy vector for the search since we're only filtering, not doing similarity search
            var dummyVector = new ReadOnlyMemory<float>(new float[1536]);
            var results = await _collection.VectorizedSearchAsync(dummyVector, searchOptions);

            return results.Select(r => r.Record).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get document sections for document: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<DocumentSectionVector?> GetDocumentSectionAsync(Guid sectionId)
    {
        try
        {
            return await _collection.GetAsync(sectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get document section: {SectionId}", sectionId);
            throw;
        }
    }

    public async Task<bool> DeleteDocumentSectionsAsync(Guid documentId)
    {
        try
        {
            // First get all sections for the document
            var sections = await GetDocumentSectionsAsync(documentId);
            var sectionIds = sections.Select(s => s.Id).ToList();

            if (sectionIds.Any())
            {
                await _collection.DeleteBatchAsync(sectionIds);
                _logger.LogInformation("Deleted {Count} document sections for document: {DocumentId}", 
                    sectionIds.Count, documentId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete document sections for document: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<bool> DeleteDocumentSectionAsync(Guid sectionId)
    {
        try
        {
            await _collection.DeleteAsync(sectionId);
            _logger.LogDebug("Deleted document section: {SectionId}", sectionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete document section: {SectionId}", sectionId);
            throw;
        }
    }

    public async Task<bool> DeleteDocumentStoreSectionsAsync(Guid documentStoreId)
    {
        try
        {
            // This is a more complex operation that might require custom SQL
            // For now, we'll implement it by getting all sections and deleting them
            var filter = new VectorSearchFilter().EqualTo("DocumentStoreId", documentStoreId);
            var searchOptions = new VectorSearchOptions
            {
                Filter = filter
            };

            var dummyVector = new ReadOnlyMemory<float>(new float[1536]);
            var results = await _collection.VectorizedSearchAsync(dummyVector, searchOptions);
            var sectionIds = results.Select(r => r.Record.Id).ToList();

            if (sectionIds.Any())
            {
                await _collection.DeleteBatchAsync(sectionIds);
                _logger.LogInformation("Deleted {Count} document sections for document store: {DocumentStoreId}", 
                    sectionIds.Count, documentStoreId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete document sections for document store: {DocumentStoreId}", documentStoreId);
            throw;
        }
    }

    public async Task<int> GetDocumentSectionCountAsync(Guid? documentStoreId = null)
    {
        try
        {
            // This is a limitation of the current vector store API - no direct count method
            // We'll need to implement this by querying and counting results
            var searchOptions = new VectorSearchOptions
            {
                Top = int.MaxValue // Get all results to count them
            };

            if (documentStoreId.HasValue)
            {
                searchOptions.Filter = new VectorSearchFilter().EqualTo("DocumentStoreId", documentStoreId.Value);
            }

            var dummyVector = new ReadOnlyMemory<float>(new float[1536]);
            var results = await _collection.VectorizedSearchAsync(dummyVector, searchOptions);

            return results.Count();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get document section count");
            throw;
        }
    }

    public async Task<bool> CollectionExistsAsync()
    {
        try
        {
            return await _collection.CollectionExistsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if collection exists");
            throw;
        }
    }
}
