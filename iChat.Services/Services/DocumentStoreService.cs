using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Repositories;
using iChat.Shared.Models;

namespace iChat.Services.Services;

public class DocumentStoreService : IDocumentStoreService
{
    private readonly IRepository<DocumentStore> _documentStoreRepository;
    private readonly IRepository<Document> _documentRepository;
    private readonly ILogger<DocumentStoreService> _logger;

    public DocumentStoreService(
        IRepository<DocumentStore> documentStoreRepository,
        IRepository<Document> documentRepository,
        ILogger<DocumentStoreService> logger)
    {
        _documentStoreRepository = documentStoreRepository;
        _documentRepository = documentRepository;
        _logger = logger;
    }

    public async Task<DocumentStore?> GetDocumentStoreByIdAsync(Guid documentStoreId)
    {
        try
        {
            return await _documentStoreRepository.GetByIdAsync(documentStoreId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document store by ID: {DocumentStoreId}", documentStoreId);
            throw;
        }
    }

    public async Task<IEnumerable<DocumentStore>> GetAllDocumentStoresAsync()
    {
        try
        {
            return await _documentStoreRepository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all document stores");
            throw;
        }
    }

    public async Task<DocumentStore> CreateDocumentStoreAsync(string name, string description, Guid createdBy)
    {
        try
        {
            var existingStore = await GetDocumentStoreByNameAsync(name);
            if (existingStore != null)
            {
                throw new InvalidOperationException($"Document store with name '{name}' already exists");
            }

            var documentStore = new DocumentStore
            {
                Id = Guid.NewGuid(),
                Name = name,
                Description = description,
                IsDefault = false,
                CreatedBy = createdBy,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var createdStore = await _documentStoreRepository.AddAsync(documentStore);
            _logger.LogInformation("Created document store: {DocumentStoreId} - {Name}", createdStore.Id, createdStore.Name);
            return createdStore;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating document store: {Name}", name);
            throw;
        }
    }

    public async Task<DocumentStore> UpdateDocumentStoreAsync(DocumentStore documentStore)
    {
        try
        {
            documentStore.UpdatedAt = DateTime.UtcNow;
            var updatedStore = await _documentStoreRepository.UpdateAsync(documentStore);
            _logger.LogInformation("Updated document store: {DocumentStoreId}", updatedStore.Id);
            return updatedStore;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document store: {DocumentStoreId}", documentStore.Id);
            throw;
        }
    }

    public async Task<bool> DeleteDocumentStoreAsync(Guid documentStoreId)
    {
        try
        {
            // Check if there are documents in this store
            var documentCount = await GetDocumentCountInStoreAsync(documentStoreId);
            if (documentCount > 0)
            {
                throw new InvalidOperationException($"Cannot delete document store with {documentCount} documents. Please move or delete documents first.");
            }

            var result = await _documentStoreRepository.DeleteAsync(documentStoreId);
            if (result)
            {
                _logger.LogInformation("Deleted document store: {DocumentStoreId}", documentStoreId);
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document store: {DocumentStoreId}", documentStoreId);
            throw;
        }
    }

    public async Task<bool> DocumentStoreExistsAsync(Guid documentStoreId)
    {
        try
        {
            return await _documentStoreRepository.ExistsAsync(documentStoreId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if document store exists: {DocumentStoreId}", documentStoreId);
            throw;
        }
    }

    public async Task<DocumentStore?> GetDocumentStoreByNameAsync(string name)
    {
        try
        {
            return await _documentStoreRepository.FirstOrDefaultAsync(ds => ds.Name == name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document store by name: {Name}", name);
            throw;
        }
    }

    public async Task<IEnumerable<DocumentStore>> SearchDocumentStoresAsync(string searchTerm)
    {
        try
        {
            return await _documentStoreRepository.FindAsync(ds => 
                ds.Name.Contains(searchTerm) || 
                ds.Description.Contains(searchTerm));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching document stores with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<int> GetDocumentStoreCountAsync()
    {
        try
        {
            return await _documentStoreRepository.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document store count");
            throw;
        }
    }

    public async Task<int> GetDocumentCountInStoreAsync(Guid documentStoreId)
    {
        try
        {
            return await _documentRepository.CountAsync(d => d.DocumentStoreId == documentStoreId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document count in store: {DocumentStoreId}", documentStoreId);
            throw;
        }
    }

    public async Task<DocumentStore?> GetDefaultDocumentStoreAsync()
    {
        try
        {
            return await _documentStoreRepository.FirstOrDefaultAsync(ds => ds.IsDefault);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting default document store");
            throw;
        }
    }

    public async Task<DocumentStore> SetDefaultDocumentStoreAsync(Guid documentStoreId)
    {
        try
        {
            // First, unset any existing default
            var currentDefault = await GetDefaultDocumentStoreAsync();
            if (currentDefault != null)
            {
                currentDefault.IsDefault = false;
                await UpdateDocumentStoreAsync(currentDefault);
            }

            // Set the new default
            var newDefault = await GetDocumentStoreByIdAsync(documentStoreId);
            if (newDefault == null)
            {
                throw new ArgumentException($"Document store with ID {documentStoreId} not found");
            }

            newDefault.IsDefault = true;
            var updatedStore = await UpdateDocumentStoreAsync(newDefault);
            
            _logger.LogInformation("Set default document store: {DocumentStoreId}", documentStoreId);
            return updatedStore;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting default document store: {DocumentStoreId}", documentStoreId);
            throw;
        }
    }
}
