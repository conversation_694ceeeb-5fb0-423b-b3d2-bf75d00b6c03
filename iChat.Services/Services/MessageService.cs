using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Repositories;
using iChat.Shared.Models;
using iChat.Shared.Enums;

namespace iChat.Services.Services;

public class MessageService : IMessageService
{
    private readonly IRepository<Message> _messageRepository;
    private readonly IEscalatedQueryService _escalatedQueryService;
    private readonly ILogger<MessageService> _logger;

    public MessageService(
        IRepository<Message> messageRepository,
        IEscalatedQueryService escalatedQueryService,
        ILogger<MessageService> logger)
    {
        _messageRepository = messageRepository;
        _escalatedQueryService = escalatedQueryService;
        _logger = logger;
    }

    public async Task<Message?> GetMessageByIdAsync(Guid messageId)
    {
        try
        {
            return await _messageRepository.GetByIdAsync(messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message by ID: {MessageId}", messageId);
            throw;
        }
    }

    public async Task<IEnumerable<Message>> GetMessagesBySessionAsync(Guid sessionId)
    {
        try
        {
            var messages = await _messageRepository.FindAsync(m => m.SessionId == sessionId);
            return messages.OrderBy(m => m.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting messages by session: {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<IEnumerable<Message>> GetMessagesByUserAsync(Guid userId)
    {
        try
        {
            // This would require a join with ChatSession to get user messages
            // For now, we'll implement a basic version
            var messages = await _messageRepository.GetAllAsync();
            return messages.Where(m => m.Session?.UserId == userId).OrderBy(m => m.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting messages by user: {UserId}", userId);
            throw;
        }
    }

    public async Task<Message> CreateMessageAsync(Guid sessionId, string content, MessageRole role, double? confidence = null)
    {
        try
        {
            var message = new Message
            {
                Id = Guid.NewGuid(),
                SessionId = sessionId,
                Content = content,
                Role = role,
                Confidence = confidence,
                Escalated = false,
                CreatedAt = DateTime.UtcNow
            };

            var createdMessage = await _messageRepository.AddAsync(message);
            _logger.LogInformation("Created message: {MessageId} in session: {SessionId}", createdMessage.Id, sessionId);
            return createdMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating message in session: {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<Message> UpdateMessageAsync(Message message)
    {
        try
        {
            var updatedMessage = await _messageRepository.UpdateAsync(message);
            _logger.LogInformation("Updated message: {MessageId}", updatedMessage.Id);
            return updatedMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating message: {MessageId}", message.Id);
            throw;
        }
    }

    public async Task<bool> DeleteMessageAsync(Guid messageId)
    {
        try
        {
            var result = await _messageRepository.DeleteAsync(messageId);
            if (result)
            {
                _logger.LogInformation("Deleted message: {MessageId}", messageId);
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting message: {MessageId}", messageId);
            throw;
        }
    }

    public async Task<bool> MessageExistsAsync(Guid messageId)
    {
        try
        {
            return await _messageRepository.ExistsAsync(messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if message exists: {MessageId}", messageId);
            throw;
        }
    }

    public async Task<IEnumerable<Message>> GetEscalatedMessagesAsync()
    {
        try
        {
            return await _messageRepository.FindAsync(m => m.Escalated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated messages");
            throw;
        }
    }

    public async Task<Message> EscalateMessageAsync(Guid messageId)
    {
        try
        {
            var message = await GetMessageByIdAsync(messageId);
            if (message == null)
            {
                throw new ArgumentException($"Message with ID {messageId} not found");
            }

            message.Escalated = true;
            var updatedMessage = await UpdateMessageAsync(message);

            // Create escalated query
            await _escalatedQueryService.CreateEscalatedQueryAsync(messageId);

            _logger.LogInformation("Escalated message: {MessageId}", messageId);
            return updatedMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error escalating message: {MessageId}", messageId);
            throw;
        }
    }

    public async Task<IEnumerable<Message>> SearchMessagesAsync(string searchTerm)
    {
        try
        {
            return await _messageRepository.FindAsync(m => m.Content.Contains(searchTerm));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching messages with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<int> GetMessageCountAsync()
    {
        try
        {
            return await _messageRepository.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message count");
            throw;
        }
    }

    public async Task<int> GetMessageCountBySessionAsync(Guid sessionId)
    {
        try
        {
            return await _messageRepository.CountAsync(m => m.SessionId == sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message count by session: {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<IEnumerable<Message>> GetRecentMessagesAsync(Guid sessionId, int count = 50)
    {
        try
        {
            var messages = await _messageRepository.FindAsync(m => m.SessionId == sessionId);
            return messages.OrderByDescending(m => m.CreatedAt).Take(count).OrderBy(m => m.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent messages for session: {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<double> GetAverageConfidenceAsync(Guid sessionId)
    {
        try
        {
            var messages = await _messageRepository.FindAsync(m => m.SessionId == sessionId && m.Confidence.HasValue);
            return messages.Any() ? messages.Average(m => m.Confidence!.Value) : 0.0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting average confidence for session: {SessionId}", sessionId);
            throw;
        }
    }
}
