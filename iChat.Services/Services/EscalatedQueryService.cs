using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Repositories;
using iChat.Shared.Models;
using iChat.Shared.Enums;

namespace iChat.Services.Services;

public class EscalatedQueryService : IEscalatedQueryService
{
    private readonly IRepository<EscalatedQuery> _escalatedQueryRepository;
    private readonly ILogger<EscalatedQueryService> _logger;

    public EscalatedQueryService(
        IRepository<EscalatedQuery> escalatedQueryRepository,
        ILogger<EscalatedQueryService> logger)
    {
        _escalatedQueryRepository = escalatedQueryRepository;
        _logger = logger;
    }

    public async Task<EscalatedQuery?> GetEscalatedQueryByIdAsync(Guid escalatedQueryId)
    {
        try
        {
            return await _escalatedQueryRepository.GetByIdAsync(escalatedQueryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated query by ID: {EscalatedQueryId}", escalatedQueryId);
            throw;
        }
    }

    public async Task<EscalatedQuery?> GetEscalatedQueryByMessageIdAsync(Guid messageId)
    {
        try
        {
            return await _escalatedQueryRepository.FirstOrDefaultAsync(eq => eq.MessageId == messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated query by message ID: {MessageId}", messageId);
            throw;
        }
    }

    public async Task<IEnumerable<EscalatedQuery>> GetAllEscalatedQueriesAsync()
    {
        try
        {
            return await _escalatedQueryRepository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all escalated queries");
            throw;
        }
    }

    public async Task<IEnumerable<EscalatedQuery>> GetEscalatedQueriesByStatusAsync(QueryStatus status)
    {
        try
        {
            return await _escalatedQueryRepository.FindAsync(eq => eq.Status == status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated queries by status: {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<EscalatedQuery>> GetEscalatedQueriesAssignedToAsync(Guid userId)
    {
        try
        {
            return await _escalatedQueryRepository.FindAsync(eq => eq.AssignedTo == userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated queries assigned to user: {UserId}", userId);
            throw;
        }
    }

    public async Task<EscalatedQuery> CreateEscalatedQueryAsync(Guid messageId, Guid? assignedTo = null)
    {
        try
        {
            // Check if escalated query already exists for this message
            var existingQuery = await GetEscalatedQueryByMessageIdAsync(messageId);
            if (existingQuery != null)
            {
                throw new InvalidOperationException($"Escalated query already exists for message {messageId}");
            }

            var escalatedQuery = new EscalatedQuery
            {
                Id = Guid.NewGuid(),
                MessageId = messageId,
                Status = QueryStatus.Open,
                AssignedTo = assignedTo,
                CreatedAt = DateTime.UtcNow
            };

            var createdQuery = await _escalatedQueryRepository.AddAsync(escalatedQuery);
            _logger.LogInformation("Created escalated query: {EscalatedQueryId} for message: {MessageId}", createdQuery.Id, messageId);
            return createdQuery;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating escalated query for message: {MessageId}", messageId);
            throw;
        }
    }

    public async Task<EscalatedQuery> UpdateEscalatedQueryAsync(EscalatedQuery escalatedQuery)
    {
        try
        {
            var updatedQuery = await _escalatedQueryRepository.UpdateAsync(escalatedQuery);
            _logger.LogInformation("Updated escalated query: {EscalatedQueryId}", updatedQuery.Id);
            return updatedQuery;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating escalated query: {EscalatedQueryId}", escalatedQuery.Id);
            throw;
        }
    }

    public async Task<EscalatedQuery> AssignEscalatedQueryAsync(Guid escalatedQueryId, Guid assignedTo)
    {
        try
        {
            var escalatedQuery = await GetEscalatedQueryByIdAsync(escalatedQueryId);
            if (escalatedQuery == null)
            {
                throw new ArgumentException($"Escalated query with ID {escalatedQueryId} not found");
            }

            escalatedQuery.AssignedTo = assignedTo;
            escalatedQuery.Status = QueryStatus.InProgress;

            var updatedQuery = await UpdateEscalatedQueryAsync(escalatedQuery);
            _logger.LogInformation("Assigned escalated query: {EscalatedQueryId} to user: {UserId}", escalatedQueryId, assignedTo);
            return updatedQuery;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning escalated query: {EscalatedQueryId} to user: {UserId}", escalatedQueryId, assignedTo);
            throw;
        }
    }

    public async Task<EscalatedQuery> ResolveEscalatedQueryAsync(Guid escalatedQueryId, string resolution)
    {
        try
        {
            var escalatedQuery = await GetEscalatedQueryByIdAsync(escalatedQueryId);
            if (escalatedQuery == null)
            {
                throw new ArgumentException($"Escalated query with ID {escalatedQueryId} not found");
            }

            escalatedQuery.Status = QueryStatus.Resolved;
            escalatedQuery.Resolution = resolution;
            escalatedQuery.ResolvedAt = DateTime.UtcNow;

            var updatedQuery = await UpdateEscalatedQueryAsync(escalatedQuery);
            _logger.LogInformation("Resolved escalated query: {EscalatedQueryId}", escalatedQueryId);
            return updatedQuery;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving escalated query: {EscalatedQueryId}", escalatedQueryId);
            throw;
        }
    }

    public async Task<bool> DeleteEscalatedQueryAsync(Guid escalatedQueryId)
    {
        try
        {
            var result = await _escalatedQueryRepository.DeleteAsync(escalatedQueryId);
            if (result)
            {
                _logger.LogInformation("Deleted escalated query: {EscalatedQueryId}", escalatedQueryId);
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting escalated query: {EscalatedQueryId}", escalatedQueryId);
            throw;
        }
    }

    public async Task<bool> EscalatedQueryExistsAsync(Guid escalatedQueryId)
    {
        try
        {
            return await _escalatedQueryRepository.ExistsAsync(escalatedQueryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if escalated query exists: {EscalatedQueryId}", escalatedQueryId);
            throw;
        }
    }

    public async Task<int> GetEscalatedQueryCountAsync()
    {
        try
        {
            return await _escalatedQueryRepository.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated query count");
            throw;
        }
    }

    public async Task<int> GetEscalatedQueryCountByStatusAsync(QueryStatus status)
    {
        try
        {
            return await _escalatedQueryRepository.CountAsync(eq => eq.Status == status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated query count by status: {Status}", status);
            throw;
        }
    }

    public async Task<int> GetEscalatedQueryCountAssignedToAsync(Guid userId)
    {
        try
        {
            return await _escalatedQueryRepository.CountAsync(eq => eq.AssignedTo == userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escalated query count assigned to user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<EscalatedQuery>> GetUnassignedEscalatedQueriesAsync()
    {
        try
        {
            return await _escalatedQueryRepository.FindAsync(eq => eq.AssignedTo == null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unassigned escalated queries");
            throw;
        }
    }

    public async Task<IEnumerable<EscalatedQuery>> GetOverdueEscalatedQueriesAsync(TimeSpan overdueThreshold)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow - overdueThreshold;
            return await _escalatedQueryRepository.FindAsync(eq => 
                eq.Status != QueryStatus.Resolved && 
                eq.CreatedAt < cutoffTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting overdue escalated queries");
            throw;
        }
    }
}
