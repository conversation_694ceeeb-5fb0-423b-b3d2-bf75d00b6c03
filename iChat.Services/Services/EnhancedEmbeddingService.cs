using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Models;
using iChat.Shared.Models;

namespace iChat.Services.Services;

/// <summary>
/// Enhanced embedding service that properly integrates with the vector store
/// This service coordinates between the traditional database and vector store
/// </summary>
public class EnhancedEmbeddingService : IEmbeddingService
{
    private readonly IEmbeddingService _baseEmbeddingService;
    private readonly IVectorStoreService _vectorStoreService;
    private readonly IDocumentService _documentService;
    private readonly ILogger<EnhancedEmbeddingService> _logger;

    public EnhancedEmbeddingService(
        EmbeddingService baseEmbeddingService,
        IVectorStoreService vectorStoreService,
        IDocumentService documentService,
        ILogger<EnhancedEmbeddingService> logger)
    {
        _baseEmbeddingService = baseEmbeddingService;
        _vectorStoreService = vectorStoreService;
        _documentService = documentService;
        _logger = logger;
    }

    public async Task<float[]> GenerateEmbeddingAsync(string text)
    {
        return await _baseEmbeddingService.GenerateEmbeddingAsync(text);
    }

    public async Task<IEnumerable<float[]>> GenerateEmbeddingsAsync(IEnumerable<string> texts)
    {
        return await _baseEmbeddingService.GenerateEmbeddingsAsync(texts);
    }

    public async Task<DocumentSection> CreateDocumentSectionAsync(Guid documentId, string content, int sectionIndex, int tokenCount)
    {
        try
        {
            // Get document metadata for vector store
            var document = await _documentService.GetDocumentByIdAsync(documentId);
            if (document == null)
            {
                throw new ArgumentException($"Document with ID {documentId} not found", nameof(documentId));
            }

            var embedding = await GenerateEmbeddingAsync(content);
            
            var documentSection = new DocumentSection
            {
                Id = Guid.NewGuid(),
                DocumentId = documentId,
                Content = content,
                SectionIndex = sectionIndex,
                TokenCount = tokenCount,
                Embedding = embedding,
                CreatedAt = DateTime.UtcNow
            };

            // Store in traditional database first
            var createdSection = await _baseEmbeddingService.CreateDocumentSectionAsync(documentId, content, sectionIndex, tokenCount);
            
            // Store in vector store with proper metadata
            var vectorSection = DocumentSectionVector.FromDocumentSection(
                createdSection, 
                document.Title,
                document.DocumentStoreId);
            
            await _vectorStoreService.UpsertDocumentSectionAsync(vectorSection);
            
            _logger.LogInformation("Created document section with vector store: {DocumentSectionId} for document: {DocumentId}", 
                createdSection.Id, documentId);
            
            return createdSection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating enhanced document section for document: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<IEnumerable<DocumentSection>> GetDocumentSectionsAsync(Guid documentId)
    {
        return await _baseEmbeddingService.GetDocumentSectionsAsync(documentId);
    }

    public async Task<IEnumerable<DocumentSection>> SearchSimilarSectionsAsync(string query, int maxResults = 10, double similarityThreshold = 0.7)
    {
        try
        {
            // Use vector store for efficient similarity search
            var vectorResults = await _vectorStoreService.SearchSimilarSectionsAsync(query, maxResults, similarityThreshold);
            
            // Convert vector store results back to DocumentSection entities
            var documentSections = vectorResults.Select(vr => vr.ToDocumentSection()).ToList();
            
            _logger.LogInformation("Found {Count} similar sections for query: {Query}", documentSections.Count, query);
            return documentSections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching similar sections for query: {Query}", query);
            throw;
        }
    }

    public async Task<IEnumerable<DocumentSection>> SearchSimilarSectionsAsync(float[] queryEmbedding, int maxResults = 10, double similarityThreshold = 0.7)
    {
        try
        {
            // Convert float array to ReadOnlyMemory for vector store
            var queryEmbeddingMemory = new ReadOnlyMemory<float>(queryEmbedding);
            
            // Use vector store for similarity search
            var vectorResults = await _vectorStoreService.SearchSimilarSectionsAsync(queryEmbeddingMemory, maxResults, similarityThreshold);
            
            // Convert vector store results back to DocumentSection entities
            var documentSections = vectorResults.Select(vr => vr.ToDocumentSection()).ToList();
            
            _logger.LogInformation("Found {Count} similar sections using provided embedding", documentSections.Count);
            return documentSections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching similar sections with embedding");
            throw;
        }
    }

    public async Task<bool> ProcessDocumentSectionsAsync(Guid documentId)
    {
        return await _baseEmbeddingService.ProcessDocumentSectionsAsync(documentId);
    }

    public async Task<double> CalculateSimilarityAsync(float[] embedding1, float[] embedding2)
    {
        return await _baseEmbeddingService.CalculateSimilarityAsync(embedding1, embedding2);
    }

    public async Task<int> GetTokenCountAsync(string text)
    {
        return await _baseEmbeddingService.GetTokenCountAsync(text);
    }

    public async Task<IEnumerable<string>> SplitTextIntoSectionsAsync(string text, int maxTokensPerSection = 500)
    {
        return await _baseEmbeddingService.SplitTextIntoSectionsAsync(text, maxTokensPerSection);
    }

    public async Task<bool> DeleteDocumentSectionsAsync(Guid documentId)
    {
        try
        {
            // Delete from traditional database
            var result = await _baseEmbeddingService.DeleteDocumentSectionsAsync(documentId);
            
            // Delete from vector store
            await _vectorStoreService.DeleteDocumentSectionsAsync(documentId);
            
            _logger.LogInformation("Deleted document sections from both stores for document: {DocumentId}", documentId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting enhanced document sections for document: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<int> GetEmbeddingDimensionsAsync()
    {
        return await _baseEmbeddingService.GetEmbeddingDimensionsAsync();
    }

    /// <summary>
    /// Search similar sections within a specific document store
    /// </summary>
    public async Task<IEnumerable<DocumentSection>> SearchSimilarSectionsInStoreAsync(
        string query, 
        Guid documentStoreId, 
        int maxResults = 10, 
        double similarityThreshold = 0.7)
    {
        try
        {
            // Use vector store for efficient similarity search with store filtering
            var vectorResults = await _vectorStoreService.SearchSimilarSectionsAsync(
                query, maxResults, similarityThreshold, documentStoreId);
            
            // Convert vector store results back to DocumentSection entities
            var documentSections = vectorResults.Select(vr => vr.ToDocumentSection()).ToList();
            
            _logger.LogInformation("Found {Count} similar sections in store {StoreId} for query: {Query}", 
                documentSections.Count, documentStoreId, query);
            
            return documentSections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching similar sections in store {StoreId} for query: {Query}", 
                documentStoreId, query);
            throw;
        }
    }

    /// <summary>
    /// Initialize the vector store (should be called during application startup)
    /// </summary>
    public async Task InitializeVectorStoreAsync()
    {
        try
        {
            await _vectorStoreService.InitializeAsync();
            _logger.LogInformation("Vector store initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize vector store");
            throw;
        }
    }
}
