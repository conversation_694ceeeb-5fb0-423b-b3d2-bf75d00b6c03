using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Repositories;
using iChat.Shared.Models;

namespace iChat.Services.Services;

public class DocumentService : IDocumentService
{
    private readonly IRepository<Document> _documentRepository;
    private readonly IEmbeddingService _embeddingService;
    private readonly ILogger<DocumentService> _logger;

    private static readonly string[] SupportedMimeTypes = {
        "text/plain",
        "text/markdown",
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    };

    public DocumentService(
        IRepository<Document> documentRepository,
        IEmbeddingService embeddingService,
        ILogger<DocumentService> logger)
    {
        _documentRepository = documentRepository;
        _embeddingService = embeddingService;
        _logger = logger;
    }

    public async Task<Document?> GetDocumentByIdAsync(Guid documentId)
    {
        try
        {
            return await _documentRepository.GetByIdAsync(documentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document by ID: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<IEnumerable<Document>> GetDocumentsByUserAsync(Guid userId)
    {
        try
        {
            return await _documentRepository.FindAsync(d => d.UploadedBy == userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<Document>> GetDocumentsByStoreAsync(Guid documentStoreId)
    {
        try
        {
            return await _documentRepository.FindAsync(d => d.DocumentStoreId == documentStoreId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by store: {DocumentStoreId}", documentStoreId);
            throw;
        }
    }

    public async Task<IEnumerable<Document>> GetAllDocumentsAsync()
    {
        try
        {
            return await _documentRepository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all documents");
            throw;
        }
    }

    public async Task<Document> UploadDocumentAsync(string title, string content, string filePath, string mimeType, Guid uploadedBy, Guid documentStoreId)
    {
        try
        {
            if (!SupportedMimeTypes.Contains(mimeType))
            {
                throw new ArgumentException($"Unsupported MIME type: {mimeType}");
            }

            var document = new Document
            {
                Id = Guid.NewGuid(),
                Title = title,
                Content = content,
                FilePath = filePath,
                MimeType = mimeType,
                UploadedBy = uploadedBy,
                DocumentStoreId = documentStoreId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var createdDocument = await _documentRepository.AddAsync(document);
            _logger.LogInformation("Uploaded document: {DocumentId} - {Title}", createdDocument.Id, createdDocument.Title);

            // Process document for embeddings in background
            _ = Task.Run(async () =>
            {
                try
                {
                    await ProcessDocumentForEmbeddingsAsync(createdDocument.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing document embeddings: {DocumentId}", createdDocument.Id);
                }
            });

            return createdDocument;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document: {Title}", title);
            throw;
        }
    }

    public async Task<Document> UpdateDocumentAsync(Document document)
    {
        try
        {
            document.UpdatedAt = DateTime.UtcNow;
            var updatedDocument = await _documentRepository.UpdateAsync(document);
            _logger.LogInformation("Updated document: {DocumentId}", updatedDocument.Id);
            return updatedDocument;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document: {DocumentId}", document.Id);
            throw;
        }
    }

    public async Task<bool> DeleteDocumentAsync(Guid documentId)
    {
        try
        {
            // Delete associated document sections first
            await _embeddingService.DeleteDocumentSectionsAsync(documentId);
            
            var result = await _documentRepository.DeleteAsync(documentId);
            if (result)
            {
                _logger.LogInformation("Deleted document: {DocumentId}", documentId);
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<bool> DocumentExistsAsync(Guid documentId)
    {
        try
        {
            return await _documentRepository.ExistsAsync(documentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if document exists: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<IEnumerable<Document>> SearchDocumentsAsync(string searchTerm)
    {
        try
        {
            return await _documentRepository.FindAsync(d => 
                d.Title.Contains(searchTerm) || 
                d.Content.Contains(searchTerm));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<Document>> SearchDocumentsByContentAsync(string content)
    {
        try
        {
            return await _documentRepository.FindAsync(d => d.Content.Contains(content));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents by content: {Content}", content);
            throw;
        }
    }

    public async Task<int> GetDocumentCountAsync()
    {
        try
        {
            return await _documentRepository.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document count");
            throw;
        }
    }

    public async Task<int> GetDocumentCountByUserAsync(Guid userId)
    {
        try
        {
            return await _documentRepository.CountAsync(d => d.UploadedBy == userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document count by user: {UserId}", userId);
            throw;
        }
    }

    public async Task<long> GetTotalDocumentSizeAsync()
    {
        try
        {
            var documents = await _documentRepository.GetAllAsync();
            return documents.Sum(d => d.Content.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total document size");
            throw;
        }
    }

    public async Task<IEnumerable<string>> GetSupportedMimeTypesAsync()
    {
        return await Task.FromResult(SupportedMimeTypes);
    }

    public async Task<bool> ProcessDocumentForEmbeddingsAsync(Guid documentId)
    {
        try
        {
            return await _embeddingService.ProcessDocumentSectionsAsync(documentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document for embeddings: {DocumentId}", documentId);
            throw;
        }
    }
}
