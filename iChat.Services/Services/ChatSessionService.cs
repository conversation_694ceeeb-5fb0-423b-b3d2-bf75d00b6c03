using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Repositories;
using iChat.Shared.Models;

namespace iChat.Services.Services;

public class ChatSessionService : IChatSessionService
{
    private readonly IRepository<ChatSession> _chatSessionRepository;
    private readonly ILogger<ChatSessionService> _logger;

    public ChatSessionService(IRepository<ChatSession> chatSessionRepository, ILogger<ChatSessionService> logger)
    {
        _chatSessionRepository = chatSessionRepository;
        _logger = logger;
    }

    public async Task<ChatSession?> GetChatSessionByIdAsync(Guid sessionId)
    {
        try
        {
            return await _chatSessionRepository.GetByIdAsync(sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat session by ID: {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<IEnumerable<ChatSession>> GetChatSessionsByUserAsync(Guid userId)
    {
        try
        {
            return await _chatSessionRepository.FindAsync(cs => cs.UserId == userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat sessions by user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<ChatSession>> GetAllChatSessionsAsync()
    {
        try
        {
            return await _chatSessionRepository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all chat sessions");
            throw;
        }
    }

    public async Task<ChatSession> CreateChatSessionAsync(Guid userId, string title)
    {
        try
        {
            var chatSession = new ChatSession
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Title = title,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var createdSession = await _chatSessionRepository.AddAsync(chatSession);
            _logger.LogInformation("Created chat session: {SessionId} - {Title}", createdSession.Id, createdSession.Title);
            return createdSession;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating chat session: {Title}", title);
            throw;
        }
    }

    public async Task<ChatSession> UpdateChatSessionAsync(ChatSession chatSession)
    {
        try
        {
            chatSession.UpdatedAt = DateTime.UtcNow;
            var updatedSession = await _chatSessionRepository.UpdateAsync(chatSession);
            _logger.LogInformation("Updated chat session: {SessionId}", updatedSession.Id);
            return updatedSession;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating chat session: {SessionId}", chatSession.Id);
            throw;
        }
    }

    public async Task<bool> DeleteChatSessionAsync(Guid sessionId)
    {
        try
        {
            var result = await _chatSessionRepository.DeleteAsync(sessionId);
            if (result)
            {
                _logger.LogInformation("Deleted chat session: {SessionId}", sessionId);
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting chat session: {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<bool> ChatSessionExistsAsync(Guid sessionId)
    {
        try
        {
            return await _chatSessionRepository.ExistsAsync(sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if chat session exists: {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<IEnumerable<ChatSession>> GetRecentChatSessionsAsync(Guid userId, int count = 10)
    {
        try
        {
            var sessions = await _chatSessionRepository.FindAsync(cs => cs.UserId == userId);
            return sessions.OrderByDescending(cs => cs.UpdatedAt).Take(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent chat sessions for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<ChatSession>> SearchChatSessionsAsync(string searchTerm)
    {
        try
        {
            return await _chatSessionRepository.FindAsync(cs => cs.Title.Contains(searchTerm));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching chat sessions with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<int> GetChatSessionCountAsync()
    {
        try
        {
            return await _chatSessionRepository.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat session count");
            throw;
        }
    }

    public async Task<int> GetChatSessionCountByUserAsync(Guid userId)
    {
        try
        {
            return await _chatSessionRepository.CountAsync(cs => cs.UserId == userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat session count by user: {UserId}", userId);
            throw;
        }
    }

    public async Task<ChatSession?> GetMostRecentChatSessionAsync(Guid userId)
    {
        try
        {
            var sessions = await _chatSessionRepository.FindAsync(cs => cs.UserId == userId);
            return sessions.OrderByDescending(cs => cs.UpdatedAt).FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting most recent chat session for user: {UserId}", userId);
            throw;
        }
    }
}
