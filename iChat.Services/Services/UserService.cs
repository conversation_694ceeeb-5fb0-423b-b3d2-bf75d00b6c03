using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Services.Repositories;
using iChat.Shared.Models;
using iChat.Shared.Enums;

namespace iChat.Services.Services;

public class UserService : IUserService
{
    private readonly IRepository<User> _userRepository;
    private readonly ILogger<UserService> _logger;

    public UserService(IRepository<User> userRepository, ILogger<UserService> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    public async Task<User?> GetUserByIdAsync(Guid userId)
    {
        try
        {
            return await _userRepository.GetByIdAsync(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        try
        {
            return await _userRepository.FirstOrDefaultAsync(u => u.Email == email);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by email: {Email}", email);
            throw;
        }
    }

    public async Task<IEnumerable<User>> GetAllUsersAsync()
    {
        try
        {
            return await _userRepository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all users");
            throw;
        }
    }

    public async Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role)
    {
        try
        {
            return await _userRepository.FindAsync(u => u.Role == role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users by role: {Role}", role);
            throw;
        }
    }

    public async Task<User> CreateUserAsync(string email, string name, UserRole role = UserRole.User)
    {
        try
        {
            var existingUser = await GetUserByEmailAsync(email);
            if (existingUser != null)
            {
                throw new InvalidOperationException($"User with email {email} already exists");
            }

            var user = new User
            {
                Id = Guid.NewGuid(),
                Email = email,
                Name = name,
                Role = role,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var createdUser = await _userRepository.AddAsync(user);
            _logger.LogInformation("Created user: {UserId} - {Email}", createdUser.Id, createdUser.Email);
            return createdUser;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user: {Email}", email);
            throw;
        }
    }

    public async Task<User> UpdateUserAsync(User user)
    {
        try
        {
            user.UpdatedAt = DateTime.UtcNow;
            var updatedUser = await _userRepository.UpdateAsync(user);
            _logger.LogInformation("Updated user: {UserId}", updatedUser.Id);
            return updatedUser;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user: {UserId}", user.Id);
            throw;
        }
    }

    public async Task<bool> DeleteUserAsync(Guid userId)
    {
        try
        {
            var result = await _userRepository.DeleteAsync(userId);
            if (result)
            {
                _logger.LogInformation("Deleted user: {UserId}", userId);
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> UserExistsAsync(Guid userId)
    {
        try
        {
            return await _userRepository.ExistsAsync(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if user exists: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> UserExistsByEmailAsync(string email)
    {
        try
        {
            return await _userRepository.ExistsAsync(u => u.Email == email);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if user exists by email: {Email}", email);
            throw;
        }
    }

    public async Task<int> GetUserCountAsync()
    {
        try
        {
            return await _userRepository.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user count");
            throw;
        }
    }

    public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm)
    {
        try
        {
            return await _userRepository.FindAsync(u => 
                u.Name.Contains(searchTerm) || 
                u.Email.Contains(searchTerm));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching users with term: {SearchTerm}", searchTerm);
            throw;
        }
    }
}
