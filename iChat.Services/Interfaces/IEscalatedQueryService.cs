using iChat.Shared.Models;
using iChat.Shared.Enums;

namespace iChat.Services.Interfaces;

public interface IEscalatedQueryService
{
    Task<EscalatedQuery?> GetEscalatedQueryByIdAsync(Guid escalatedQueryId);
    Task<EscalatedQuery?> GetEscalatedQueryByMessageIdAsync(Guid messageId);
    Task<IEnumerable<EscalatedQuery>> GetAllEscalatedQueriesAsync();
    Task<IEnumerable<EscalatedQuery>> GetEscalatedQueriesByStatusAsync(QueryStatus status);
    Task<IEnumerable<EscalatedQuery>> GetEscalatedQueriesAssignedToAsync(Guid userId);
    Task<EscalatedQuery> CreateEscalatedQueryAsync(Guid messageId, Guid? assignedTo = null);
    Task<EscalatedQuery> UpdateEscalatedQueryAsync(EscalatedQuery escalatedQuery);
    Task<EscalatedQuery> AssignEscalatedQueryAsync(Guid escalatedQueryId, Guid assignedTo);
    Task<EscalatedQuery> ResolveEscalatedQueryAsync(Guid escalatedQueryId, string resolution);
    Task<bool> DeleteEscalatedQueryAsync(Guid escalatedQueryId);
    Task<bool> EscalatedQueryExistsAsync(Guid escalatedQueryId);
    Task<int> GetEscalatedQueryCountAsync();
    Task<int> GetEscalatedQueryCountByStatusAsync(QueryStatus status);
    Task<int> GetEscalatedQueryCountAssignedToAsync(Guid userId);
    Task<IEnumerable<EscalatedQuery>> GetUnassignedEscalatedQueriesAsync();
    Task<IEnumerable<EscalatedQuery>> GetOverdueEscalatedQueriesAsync(TimeSpan overdueThreshold);
}
