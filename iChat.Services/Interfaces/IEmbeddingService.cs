using iChat.Shared.Models;

namespace iChat.Services.Interfaces;

public interface IEmbeddingService
{
    Task<float[]> GenerateEmbeddingAsync(string text);
    Task<IEnumerable<float[]>> GenerateEmbeddingsAsync(IEnumerable<string> texts);
    Task<DocumentSection> CreateDocumentSectionAsync(Guid documentId, string content, int sectionIndex, int tokenCount);
    Task<IEnumerable<DocumentSection>> GetDocumentSectionsAsync(Guid documentId);
    Task<IEnumerable<DocumentSection>> SearchSimilarSectionsAsync(string query, int maxResults = 10, double similarityThreshold = 0.7);
    Task<IEnumerable<DocumentSection>> SearchSimilarSectionsAsync(float[] queryEmbedding, int maxResults = 10, double similarityThreshold = 0.7);
    Task<bool> ProcessDocumentSectionsAsync(Guid documentId);
    Task<double> CalculateSimilarityAsync(float[] embedding1, float[] embedding2);
    Task<int> GetTokenCountAsync(string text);
    Task<IEnumerable<string>> SplitTextIntoSectionsAsync(string text, int maxTokensPerSection = 500);
    Task<bool> DeleteDocumentSectionsAsync(Guid documentId);
    Task<int> GetEmbeddingDimensionsAsync();
}
