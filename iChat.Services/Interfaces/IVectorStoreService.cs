using iChat.Services.Models;
using iChat.Shared.Models;
using Microsoft.Extensions.VectorData;

namespace iChat.Services.Interfaces;

/// <summary>
/// Service interface for vector store operations using Postgres Vector Store
/// This service handles vector-specific operations while the EmbeddingService handles business logic
/// </summary>
public interface IVectorStoreService
{
    /// <summary>
    /// Initialize the vector store collection and ensure proper indexing
    /// </summary>
    Task InitializeAsync();

    /// <summary>
    /// Store a document section in the vector store
    /// </summary>
    Task<DocumentSectionVector> UpsertDocumentSectionAsync(DocumentSectionVector sectionVector);

    /// <summary>
    /// Store multiple document sections in the vector store
    /// </summary>
    Task<IEnumerable<DocumentSectionVector>> UpsertDocumentSectionsAsync(IEnumerable<DocumentSectionVector> sectionVectors);

    /// <summary>
    /// Search for similar document sections using vector similarity
    /// </summary>
    Task<IEnumerable<DocumentSectionVector>> SearchSimilarSectionsAsync(
        ReadOnlyMemory<float> queryEmbedding, 
        int maxResults = 10, 
        double similarityThreshold = 0.7,
        Guid? documentStoreId = null);

    /// <summary>
    /// Search for similar document sections using text query (will generate embedding)
    /// </summary>
    Task<IEnumerable<DocumentSectionVector>> SearchSimilarSectionsAsync(
        string query, 
        int maxResults = 10, 
        double similarityThreshold = 0.7,
        Guid? documentStoreId = null);

    /// <summary>
    /// Get document sections by document ID
    /// </summary>
    Task<IEnumerable<DocumentSectionVector>> GetDocumentSectionsAsync(Guid documentId);

    /// <summary>
    /// Get a specific document section by ID
    /// </summary>
    Task<DocumentSectionVector?> GetDocumentSectionAsync(Guid sectionId);

    /// <summary>
    /// Delete document sections by document ID
    /// </summary>
    Task<bool> DeleteDocumentSectionsAsync(Guid documentId);

    /// <summary>
    /// Delete a specific document section
    /// </summary>
    Task<bool> DeleteDocumentSectionAsync(Guid sectionId);

    /// <summary>
    /// Delete all document sections in a document store
    /// </summary>
    Task<bool> DeleteDocumentStoreSectionsAsync(Guid documentStoreId);

    /// <summary>
    /// Get count of document sections in the vector store
    /// </summary>
    Task<int> GetDocumentSectionCountAsync(Guid? documentStoreId = null);

    /// <summary>
    /// Check if the vector store collection exists and is properly configured
    /// </summary>
    Task<bool> CollectionExistsAsync();
}
