using iChat.Shared.Models;

namespace iChat.Services.Interfaces;

public interface IDocumentService
{
    Task<Document?> GetDocumentByIdAsync(Guid documentId);
    Task<IEnumerable<Document>> GetDocumentsByUserAsync(Guid userId);
    Task<IEnumerable<Document>> GetDocumentsByStoreAsync(Guid documentStoreId);
    Task<IEnumerable<Document>> GetAllDocumentsAsync();
    Task<Document> UploadDocumentAsync(string title, string content, string filePath, string mimeType, Guid uploadedBy, Guid documentStoreId);
    Task<Document> UpdateDocumentAsync(Document document);
    Task<bool> DeleteDocumentAsync(Guid documentId);
    Task<bool> DocumentExistsAsync(Guid documentId);
    Task<IEnumerable<Document>> SearchDocumentsAsync(string searchTerm);
    Task<IEnumerable<Document>> SearchDocumentsByContentAsync(string content);
    Task<int> GetDocumentCountAsync();
    Task<int> GetDocumentCountByUserAsync(Guid userId);
    Task<long> GetTotalDocumentSizeAsync();
    Task<IEnumerable<string>> GetSupportedMimeTypesAsync();
    Task<bool> ProcessDocumentForEmbeddingsAsync(Guid documentId);
}
