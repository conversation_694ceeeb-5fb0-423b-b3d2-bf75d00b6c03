using iChat.Shared.Models;
using iChat.Shared.Enums;

namespace iChat.Services.Interfaces;

public interface IUserService
{
    Task<User?> GetUserByIdAsync(Guid userId);
    Task<User?> GetUserByEmailAsync(string email);
    Task<IEnumerable<User>> GetAllUsersAsync();
    Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role);
    Task<User> CreateUserAsync(string email, string name, UserRole role = UserRole.User);
    Task<User> UpdateUserAsync(User user);
    Task<bool> DeleteUserAsync(Guid userId);
    Task<bool> UserExistsAsync(Guid userId);
    Task<bool> UserExistsByEmailAsync(string email);
    Task<int> GetUserCountAsync();
    Task<IEnumerable<User>> SearchUsersAsync(string searchTerm);
}
