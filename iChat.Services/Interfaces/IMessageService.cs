using iChat.Shared.Models;
using iChat.Shared.Enums;

namespace iChat.Services.Interfaces;

public interface IMessageService
{
    Task<Message?> GetMessageByIdAsync(Guid messageId);
    Task<IEnumerable<Message>> GetMessagesBySessionAsync(Guid sessionId);
    Task<IEnumerable<Message>> GetMessagesByUserAsync(Guid userId);
    Task<Message> CreateMessageAsync(Guid sessionId, string content, MessageRole role, double? confidence = null);
    Task<Message> UpdateMessageAsync(Message message);
    Task<bool> DeleteMessageAsync(Guid messageId);
    Task<bool> MessageExistsAsync(Guid messageId);
    Task<IEnumerable<Message>> GetEscalatedMessagesAsync();
    Task<Message> EscalateMessageAsync(Guid messageId);
    Task<IEnumerable<Message>> SearchMessagesAsync(string searchTerm);
    Task<int> GetMessageCountAsync();
    Task<int> GetMessageCountBySessionAsync(Guid sessionId);
    Task<IEnumerable<Message>> GetRecentMessagesAsync(Guid sessionId, int count = 50);
    Task<double> GetAverageConfidenceAsync(Guid sessionId);
}
