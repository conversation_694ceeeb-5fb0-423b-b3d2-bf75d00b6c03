using iChat.Shared.Models;

namespace iChat.Services.Interfaces;

public interface IChatSessionService
{
    Task<ChatSession?> GetChatSessionByIdAsync(Guid sessionId);
    Task<IEnumerable<ChatSession>> GetChatSessionsByUserAsync(Guid userId);
    Task<IEnumerable<ChatSession>> GetAllChatSessionsAsync();
    Task<ChatSession> CreateChatSessionAsync(Guid userId, string title);
    Task<ChatSession> UpdateChatSessionAsync(ChatSession chatSession);
    Task<bool> DeleteChatSessionAsync(Guid sessionId);
    Task<bool> ChatSessionExistsAsync(Guid sessionId);
    Task<IEnumerable<ChatSession>> GetRecentChatSessionsAsync(Guid userId, int count = 10);
    Task<IEnumerable<ChatSession>> SearchChatSessionsAsync(string searchTerm);
    Task<int> GetChatSessionCountAsync();
    Task<int> GetChatSessionCountByUserAsync(Guid userId);
    Task<ChatSession?> GetMostRecentChatSessionAsync(Guid userId);
}
